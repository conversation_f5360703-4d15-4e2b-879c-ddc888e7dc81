{"name": "koishi-app", "version": "0.0.0", "packageManager": "yarn@4.1.0", "private": true, "files": [".env", "koishi.yml"], "license": "AGPL-3.0", "workspaces": ["external/*", "external/*/external/*", "external/*/external/*/packages/*", "external/*/external/*/plugins/*", "external/*/packages/*", "external/*/plugins/*", "external/satori/adapters/*", "external/koishi/plugins/*", "external/koishi/plugins/database/*", "packages/*", "packages/@*/*", "plugins/*", "plugins/@*/*"], "scripts": {"new": "koishi-scripts new", "setup": "koishi-scripts setup", "clone": "koishi-scripts clone", "build": "yakumo build", "clean": "yakumo clean", "bump": "yakumo version", "dep": "yakumo upgrade", "pub": "yakumo publish", "dev": "cross-env NODE_ENV=development koishi start -r esbuild-register -r yml-register", "start": "koishi start"}, "devDependencies": {"@koishijs/client": "^5.30.4", "@koishijs/plugin-hmr": "^1.2.9", "@koishijs/scripts": "^4.6.1", "@types/node": "^22.7.5", "@types/react": "^18.2.70", "cross-env": "^7.0.3", "esbuild": "^0.23.1", "esbuild-register": "npm:@shigma/esbuild-register@^1.1.1", "tsx": "^4.16.2", "typescript": "^5.6.2", "yakumo": "^1.0.0-beta.21", "yakumo-esbuild": "^1.0.0-beta.7", "yakumo-tsc": "^1.0.0-beta.5", "yml-register": "^1.2.5"}, "dependencies": {"@koishijs/plugin-actions": "^0.0.2", "@koishijs/plugin-adapter-discord": "^4.5.10", "@koishijs/plugin-adapter-kook": "^4.6.5", "@koishijs/plugin-adapter-qq": "^4.9.2", "@koishijs/plugin-adapter-satori": "^1.5.1", "@koishijs/plugin-adapter-telegram": "^4.5.8", "@koishijs/plugin-admin": "^2.0.0-beta.4", "@koishijs/plugin-analytics": "^2.0.6", "@koishijs/plugin-auth": "^4.1.6", "@koishijs/plugin-bind": "^1.5.1", "@koishijs/plugin-commands": "^3.5.4", "@koishijs/plugin-config": "^2.8.6", "@koishijs/plugin-console": "^5.30.4", "@koishijs/plugin-database-sqlite": "^4.6.0", "@koishijs/plugin-explorer": "^1.5.5", "@koishijs/plugin-help": "^2.4.5", "@koishijs/plugin-http": "^0.6.3", "@koishijs/plugin-insight": "^3.5.2", "@koishijs/plugin-inspect": "^1.1.7", "@koishijs/plugin-locales": "^2.5.3", "@koishijs/plugin-logger": "^2.6.9", "@koishijs/plugin-market": "^2.11.4", "@koishijs/plugin-notifier": "^1.2.1", "@koishijs/plugin-oobe": "^0.0.2", "@koishijs/plugin-proxy-agent": "^0.3.3", "@koishijs/plugin-sandbox": "^3.4.1", "@koishijs/plugin-server": "^3.2.4", "@koishijs/plugin-server-satori": "^2.9.0", "@koishijs/plugin-server-temp": "^1.5.0", "@koishijs/plugin-status": "^7.4.10", "koishi": "^4.18.7", "koishi-plugin-adapter-onebot": "6.8.0", "koishi-plugin-android": "^0.0.1", "koishi-plugin-assets-local": "^3.3.2", "koishi-plugin-auto-tata-group-name": "1.2.2", "koishi-plugin-dataview": "^2.6.1", "koishi-plugin-desktop": "^1.0.0", "koishi-plugin-puppeteer": "^3.9.0", "koishi-plugin-rate-limit": "^2.0.4", "koishi-plugin-repeater-tata": "1.0.0", "koishi-plugin-telemetry": "^0.2.3", "koishi-plugin-theme-vanilla": "^1.1.0"}}