name: Tag

on:
  push:
    branches:
      - master

jobs:
  release:
    runs-on: ubuntu-latest

    steps:
      - name: Check out
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - id: version
        name: Check version
        run: bash .github/workflows/tag.sh

      - name: Create release
        if: ${{ steps.version.outputs.tag }}
        run: >
          curl
          -X POST
          -H "Accept: application/vnd.github.v3+json"
          -H "Authorization: token ${{ secrets.WORKFLOW_TOKEN }}"
          https://api.github.com/repos/${{ github.repository }}/releases
          -d '{
            "name": "${{ steps.version.outputs.tag }}",
            "tag_name": "${{ steps.version.outputs.tag }}",
            "target_commitish": "${{ github.sha }}"
          }'
