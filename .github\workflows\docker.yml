name: Docker

on:
  workflow_dispatch:

jobs:
  publish-dockerhub:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Docker buildx
        uses: docker/setup-buildx-action@v3
      - name: Docker Hub login
        env:
          DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
        run: |
          echo "${DOCKER_PASSWORD}" | docker login \
            --username "${DOCKER_USERNAME}" \
            --password-stdin
      - name: Run buildx and push
        env:
          DOCKER_REPO: koishijs/koishi
          GITHUB_REPO: ${{ github.repository }}
        run: |
          TAG=$(curl "https://api.github.com/repos/${GITHUB_REPO}/releases/latest" | jq -r '.tag_name')
          NAME=$(cat package.json | jq -r '.name' | sed -E 's/.+\///')
          LINK="https://github.com/${GITHUB_REPO}/releases/download/$TAG/$NAME-$TAG-linux-"
          docker buildx build \
            --build-arg LINK=$LINK \
            --output "type=image,push=true" \
            --platform linux/amd64,linux/arm64 \
            --tag ${DOCKER_REPO}:$TAG \
            --tag ${DOCKER_REPO}:latest \
            --file ./docker/Dockerfile \
            ./docker
          docker buildx build \
            --build-arg LINK=$LINK \
            --output "type=image,push=true" \
            --platform linux/amd64,linux/arm64 \
            --tag ${DOCKER_REPO}:$TAG-lite \
            --tag ${DOCKER_REPO}:latest-lite \
            --file ./docker/Dockerfile.lite \
            ./docker
      - name: Docker Hub logout
        if: always()
        run: docker logout
