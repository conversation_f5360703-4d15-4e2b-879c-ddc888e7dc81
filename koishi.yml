plugins:
  group:server:
    server:323kyy:
      port: 5140
      maxPort: 5149
    ~server-satori:v8ty2u: {}
    ~server-temp:xkouel: {}
  group:basic:
    ~admin:u9ujud: {}
    ~bind:bggjzi: {}
    commands:t28fd7: {}
    help:ff3bs7: {}
    http:0mmoly: {}
    ~inspect:o62nyo: {}
    locales:9hd8z0: {}
    proxy-agent:v6s18m: {}
    rate-limit:blptcj: {}
    telemetry:fthe11: {}
  group:console:
    actions:jadf49: {}
    analytics:o140tn: {}
    android:kqwh3s:
      $if: env.KOISHI_AGENT?.includes('Android')
    ~auth:y1hl39: {}
    config:cwtr3f: {}
    console:3dkdmv:
      open: true
    dataview:agncyr: {}
    desktop:nu4exs:
      $if: env.KOISHI_AGENT?.includes('Desktop')
    explorer:nvmelb: {}
    logger:12do3u: {}
    insight:sqbfrn: {}
    market:jkqr6z:
      search:
        endpoint: https://registry.koishi.t4wefan.pub/index.json
    notifier:poe905: {}
    oobe:30x2ua: {}
    sandbox:wsbqko: {}
    status:b76shp: {}
    theme-vanilla:6125sj: {}
  group:storage:
    ~database-mongo:nokll7:
      database: koishi
    ~database-mysql:1lkggg:
      database: koishi
    ~database-postgres:dgvwda:
      database: koishi
    database-sqlite:ixvuw7:
      path: data/koishi.db
    assets-local:gocxwe: {}
  group:adapter:
    ~adapter-dingtalk:r884v2: {}
    ~adapter-discord:zi4u56: {}
    ~adapter-kook:f1hqds: {}
    ~adapter-lark:jgqm42: {}
    ~adapter-line:qsqsv8: {}
    ~adapter-mail:jb33z4: {}
    ~adapter-matrix:but6rb: {}
    ~adapter-qq:5h3qb4:
      id: '721115531'
      token: 89088046ch
      type: private
      endpoint: http://127.0.0.1:3001/
    ~adapter-satori:n6c0qj: {}
    ~adapter-slack:vhk714: {}
    ~adapter-telegram:ixvibb: {}
    ~adapter-wechat-official:qayhkl: {}
    ~adapter-wecom:us4lyo: {}
    ~adapter-whatsapp:o6fd32: {}
    ~adapter-zulip:l3vcwl: {}
  group:develop:
    $if: env.NODE_ENV === 'development'
    hmr:k774i6:
      root: .
  adapter-onebot:vjnf9o:
    selfId: '752115531'
    token: 89088046ch
    protocol: ws
    endpoint: http://127.0.0.1:3001/
    timeout: 20
  auto-tata-group-name:6zndvg:
    guildTemplates:
      - guildId: '123213'
      - guildId: '123123'
        nameTemplate: ({count})🦦獭家123123一爱相亲相
  repeater-tata:abc123:
    guilds:
      - guildId: '332993267'
        enabled: true
    contextSize: 50
    repeatDelay: 1000
    repeatImages: true
    repeatMedia: true
