# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.3.0":
  version "2.3.0"
  resolved "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz"
  integrity sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@antfu/install-pkg@^1.0.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@antfu/install-pkg/-/install-pkg-1.1.0.tgz"
  integrity sha512-MGQsmw10ZyI+EJo45CdSER4zEb+p31LpDAFp2Z3gkSd1yqVZGi0Ebx++YTEMonJy4oChEMLsxZ64j8FH6sSqtQ==
  dependencies:
    package-manager-detector "^1.3.0"
    tinyexec "^1.0.1"

"@antfu/utils@^8.1.0":
  version "8.1.1"
  resolved "https://registry.npmjs.org/@antfu/utils/-/utils-8.1.1.tgz"
  integrity sha512-Mex9nXf9vR6AhcXmMrlz/HVgYYZpVGJ6YlPgwl7UnaFpnshXs6EK/oa5Gpf3CzENMjkvEx2tQtntGnb7UtSTOQ==

"@babel/code-frame@^7.24.2":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz"
  integrity sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz"
  integrity sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz"
  integrity sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==

"@babel/parser@^7.27.5":
  version "7.27.7"
  resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.27.7.tgz"
  integrity sha512-qnzXzDXdr/po3bOTbTIQZ7+TxNKxpkN5IifVLXS+r7qwynkZfPyjZfE7hCXbo7IoO9TNcSyibgONsf2HauUd3Q==
  dependencies:
    "@babel/types" "^7.27.7"

"@babel/types@^7.27.7":
  version "7.27.7"
  resolved "https://registry.npmjs.org/@babel/types/-/types-7.27.7.tgz"
  integrity sha512-8OLQgDScAOHXnAz2cV+RfzzNMipuLVBz2biuAJFMV9bfkNf393je3VM8CLkjQodW5+iWsSJdSgSWT6rsZoXHPw==
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@cordisjs/core@^3.12.0", "@cordisjs/core@^3.17.0", "@cordisjs/core@^3.17.1", "@cordisjs/core@^3.18.1", "@cordisjs/core@3.18.1":
  version "3.18.1"
  resolved "https://registry.npmjs.org/@cordisjs/core/-/core-3.18.1.tgz"
  integrity sha512-yRuATOamFxeD1ztE2L3o1SaHuT2zw5DTXijQYxt9azVNeILbvtomfGG6sLZegrynJO9XZbNvXbOlQ7LBJDOlAg==
  dependencies:
    cosmokit "^1.6.2"

"@cordisjs/loader@^0.13.1":
  version "0.13.1"
  resolved "https://registry.npmjs.org/@cordisjs/loader/-/loader-0.13.1.tgz"
  integrity sha512-YGw22gT1F/jXT1UeWMjIavt7WHMfEmKve83v4PTg63SW4eC2s89ivsN4RQWi5YWe0LQNNg8LItL66FeEcgQS5w==
  dependencies:
    cosmokit "^1.6.2"
    dotenv "^16.4.5"
    js-yaml "^4.1.0"

"@cordisjs/logger@^0.3.3":
  version "0.3.3"
  resolved "https://registry.npmjs.org/@cordisjs/logger/-/logger-0.3.3.tgz"
  integrity sha512-UcyZRXAL86YLAEEta+fxJM1CcDanJvoIMD9ywCyP2AZJuib68UynENGTFo4mYkeg+8WMXrlA/aNNwENMiLUlXA==
  dependencies:
    cosmokit "^1.6.2"
    reggol "^1.7.0"

"@cordisjs/plugin-http@^0.6.0", "@cordisjs/plugin-http@^0.6.3":
  version "0.6.3"
  resolved "https://registry.npmjs.org/@cordisjs/plugin-http/-/plugin-http-0.6.3.tgz"
  integrity sha512-kmw4G1t39ZZzFGpBKUNuTv2aefEUtxa1e4qu4+bTrM5Z8uYHTRzPpyJfkj9zuZwopP/Vz271OUr2UfDN5lrJKQ==
  dependencies:
    cosmokit "^1.6.3"
    file-type "^16.5.4"
    mime-db "^1.52.0"
    ws "^8.16.0"

"@cordisjs/plugin-proxy-agent@^0.3.3":
  version "0.3.3"
  resolved "https://registry.npmjs.org/@cordisjs/plugin-proxy-agent/-/plugin-proxy-agent-0.3.3.tgz"
  integrity sha512-iOM44+bducWGdfF/eD+dH7Fhi3r5eBZqSNIXvfUjK9z0tZ06oeVk/655nAlNxff31umUXhgmRQ14KwG+ylggcQ==
  dependencies:
    http-proxy-agent "^7.0.2"
    https-proxy-agent "^7.0.4"
    socks "^2.8.1"
    socks-proxy-agent "^8.0.2"
    undici "^6.10.1"

"@cordisjs/plugin-server-temp@^0.5.0":
  version "0.5.0"
  resolved "https://registry.npmjs.org/@cordisjs/plugin-server-temp/-/plugin-server-temp-0.5.0.tgz"
  integrity sha512-KXj2o59nIjY2+YOTWRMt+5Bw8qJT06cMxTN6bmhblyBJ+vGOay61Vh4WKBQx9nZTfNyC3dFt07/iQ5qsPTpsVw==
  dependencies:
    cosmokit "^1.6.3"

"@cordisjs/plugin-server@^0.2.4", "@cordisjs/plugin-server@^0.2.7":
  version "0.2.7"
  resolved "https://registry.npmjs.org/@cordisjs/plugin-server/-/plugin-server-0.2.7.tgz"
  integrity sha512-dNMMXohaeU76ZfJ2yhoUGKKu9U4t50fmLImJjILvMbfOt5iIZdbIhf4Px1QrpHv9t7CggcIpslv0GSb01Nj8JQ==
  dependencies:
    "@koa/router" "^10.1.1"
    "@types/koa" "*"
    "@types/koa__router" "*"
    "@types/ws" "^8.5.10"
    cosmokit "^1.6.3"
    koa "^2.15.2"
    koa-body "^6.0.1"
    parseurl "^1.3.3"
    path-to-regexp "^6.3.0"
    reggol "^1.7.1"
    schemastery "^3.14.6"
    ws "^8.16.0"

"@cordisjs/schema@^0.1.1":
  version "0.1.1"
  resolved "https://registry.npmjs.org/@cordisjs/schema/-/schema-0.1.1.tgz"
  integrity sha512-AYTSyEXEASO6uVHqXBHIId+8OGFs0idtFoj3q7qee9J7OGcMjVEUsrsdiP9SnmzTR4ea17esShledUC50fco1g==
  dependencies:
    cosmokit "^1.6.2"
    schemastery "^3.14.6"

"@cordisjs/timer@^0.3.2":
  version "0.3.2"
  resolved "https://registry.npmjs.org/@cordisjs/timer/-/timer-0.3.2.tgz"
  integrity sha512-frdIvc+1AqKVHMWnNacqqZ2Cm52FfoYKPpBQ+kZgr/cFs7HXDKqetL35CLy/EZAF22l/6VTvpQOYUwS6tvVJZA==
  dependencies:
    cosmokit "^1.5.2"

"@ctrl/tinycolor@^3.4.1":
  version "3.6.1"
  resolved "https://registry.npmjs.org/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz"
  integrity sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==

"@element-plus/icons-vue@^2.3.1":
  version "2.3.1"
  resolved "https://registry.npmjs.org/@element-plus/icons-vue/-/icons-vue-2.3.1.tgz"
  integrity sha512-XxVUZv48RZAd87ucGS48jPf6pKu0yV5UCg9f4FFwtrYxXOwWuVJo6wOvSLKEoMQKjv8GsX/mhP6UsC1lRwbUWg==

"@esbuild/win32-x64@0.21.5":
  version "0.21.5"
  resolved "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz"
  integrity sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==

"@esbuild/win32-x64@0.23.1":
  version "0.23.1"
  resolved "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.23.1.tgz"
  integrity sha512-BHpFFeslkWrXWyUPnbKm+xYYVYruCinGcftSBaa8zoF9hZO4BcSCFUvHVTtzpIY6YzUnYtuEhZ+C9iEXjxnasg==

"@esbuild/win32-x64@0.25.5":
  version "0.25.5"
  resolved "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.25.5.tgz"
  integrity sha512-TXv6YnJ8ZMVdX+SXWVBo/0p8LTcrUYngpWjvm91TMjjBQii7Oz11Lw5lbDV5Y0TzuhSJHwiH4hEtC1I42mMS0g==

"@floating-ui/core@^1.7.2":
  version "1.7.2"
  resolved "https://registry.npmjs.org/@floating-ui/core/-/core-1.7.2.tgz"
  integrity sha512-wNB5ooIKHQc+Kui96jE/n69rHFWAVoxn5CAzL1Xdd8FG03cgY3MLO+GF9U3W737fYDSgPWA6MReKhBQBop6Pcw==
  dependencies:
    "@floating-ui/utils" "^0.2.10"

"@floating-ui/dom@^1.0.1":
  version "1.7.2"
  resolved "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.7.2.tgz"
  integrity sha512-7cfaOQuCS27HD7DX+6ib2OrnW+b4ZBwDNnCcT0uTyidcmyWb03FnQqJybDBoCnpdxwBSfA94UAYlRCt7mV+TbA==
  dependencies:
    "@floating-ui/core" "^1.7.2"
    "@floating-ui/utils" "^0.2.10"

"@floating-ui/utils@^0.2.10":
  version "0.2.10"
  resolved "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.10.tgz"
  integrity sha512-aGTxbpbg8/b5JfU1HXSrbH3wXZuLPJcNEcZQFMxLs3oSzgtVu6nFPkbbGGUvBcUjKV2YyB9Wxxabo+HEH9tcRQ==

"@hapi/bourne@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@hapi/bourne/-/bourne-3.0.0.tgz"
  integrity sha512-Waj1cwPXJDucOib4a3bAISsKJVb15MKi9IvmTI/7ssVEm6sywXGjVJDhl6/umt1pK1ZS7PacXU3A1PmFKHEZ2w==

"@iconify/types@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@iconify/types/-/types-2.0.0.tgz"
  integrity sha512-+wluvCrRhXrhyOmRDJ3q8mux9JkKy5SJ/v8ol2tu4FVjyYvtEzkc/3pK15ET6RKg4b4w4BmTk1+gsCUhf21Ykg==

"@iconify/utils@^2.2.1":
  version "2.3.0"
  resolved "https://registry.npmjs.org/@iconify/utils/-/utils-2.3.0.tgz"
  integrity sha512-GmQ78prtwYW6EtzXRU1rY+KwOKfz32PD7iJh6Iyqw68GiKuoZ2A6pRtzWONz5VQJbp50mEjXh/7NkumtrAgRKA==
  dependencies:
    "@antfu/install-pkg" "^1.0.0"
    "@antfu/utils" "^8.1.0"
    "@iconify/types" "^2.0.0"
    debug "^4.4.0"
    globals "^15.14.0"
    kolorist "^1.8.0"
    local-pkg "^1.0.0"
    mlly "^1.7.4"

"@intlify/core-base@9.14.4":
  version "9.14.4"
  resolved "https://registry.npmjs.org/@intlify/core-base/-/core-base-9.14.4.tgz"
  integrity sha512-vtZCt7NqWhKEtHa3SD/322DlgP5uR9MqWxnE0y8Q0tjDs9H5Lxhss+b5wv8rmuXRoHKLESNgw9d+EN9ybBbj9g==
  dependencies:
    "@intlify/message-compiler" "9.14.4"
    "@intlify/shared" "9.14.4"

"@intlify/message-compiler@9.14.4":
  version "9.14.4"
  resolved "https://registry.npmjs.org/@intlify/message-compiler/-/message-compiler-9.14.4.tgz"
  integrity sha512-vcyCLiVRN628U38c3PbahrhbbXrckrM9zpy0KZVlDk2Z0OnGwv8uQNNXP3twwGtfLsCf4gu3ci6FMIZnPaqZsw==
  dependencies:
    "@intlify/shared" "9.14.4"
    source-map-js "^1.0.2"

"@intlify/shared@9.14.4":
  version "9.14.4"
  resolved "https://registry.npmjs.org/@intlify/shared/-/shared-9.14.4.tgz"
  integrity sha512-P9zv6i1WvMc9qDBWvIgKkymjY2ptIiQ065PjDv7z7fDqH3J/HBRBN5IoiR46r/ujRcU7hCuSIZWvCAFCyuOYZA==

"@jridgewell/gen-mapping@^0.3.5":
  version "0.3.12"
  resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz"
  integrity sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.4"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz"
  integrity sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==

"@jridgewell/trace-mapping@^0.3.24":
  version "0.3.29"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz"
  integrity sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@koa/router@^10.1.1":
  version "10.1.1"
  resolved "https://registry.npmjs.org/@koa/router/-/router-10.1.1.tgz"
  integrity sha512-ORNjq5z4EmQPriKbR0ER3k4Gh7YGNhWDL7JBW+8wXDrHLbWYKYSJaOJ9aN06npF5tbTxe2JBOsurpJDAvjiXKw==
  dependencies:
    debug "^4.1.1"
    http-errors "^1.7.3"
    koa-compose "^4.1.0"
    methods "^1.1.2"
    path-to-regexp "^6.1.0"

"@koishijs/assets@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@koishijs/assets/-/assets-1.1.2.tgz"
  integrity sha512-3sH+F551ku8MMJ7ZuGMQqTh4znxb4QnFTt2c/SBExjAT7XgMUIN+nwzTuYNjjs/xqdZOqz9B45ky2GJa5/Yqmw==
  dependencies:
    file-type "^16.5.4"

"@koishijs/canvas@^0.2.0":
  version "0.2.0"
  resolved "https://registry.npmjs.org/@koishijs/canvas/-/canvas-0.2.0.tgz"
  integrity sha512-VNdzowH6Iwuh7qxIhH0tki/y4+ohQ3em/PQnnsCG7KhGNfuRaeNmFTDwH8g3B5+QTQ+2bNUll5eY3NrHcT+CqA==

"@koishijs/client@^5.30.1", "@koishijs/client@^5.30.4", "@koishijs/client@^5.30.9":
  version "5.30.9"
  resolved "https://registry.npmjs.org/@koishijs/client/-/client-5.30.9.tgz"
  integrity sha512-I3FM7DWWuRZlv96MzzRZFvX7RuLE3TOAb1WdX1VBl1ibISS5lrGSb53MrgqlMQkmo/AgAsRwIN64X7xRmphNWg==
  dependencies:
    "@koishijs/components" "1.5.21"
    "@maikolib/vite-plugin-yaml" "^1.0.1"
    "@satorijs/protocol" "^1.6.1"
    "@vitejs/plugin-vue" "^5.1.4"
    "@vueuse/core" "^11.1.0"
    cac "^6.7.14"
    cordis "^3.18.1"
    cosmokit "^1.7.2"
    element-plus "2.7.7"
    marked-vue "^1.3.0"
    ns-require "^1.1.4"
    sass "^1.82.0"
    unocss "^0.65.1"
    vite "^5.4.10"
    vue "^3.5.12"
    vue-i18n "^9.10.2"
    vue-router "^4.4.5"

"@koishijs/components@1.5.21":
  version "1.5.21"
  resolved "https://registry.npmjs.org/@koishijs/components/-/components-1.5.21.tgz"
  integrity sha512-9XtCE4vEHQWpqh6uOKlj6JeylbxCYK281q+AAsWoITiZTvZM7iqhFDTCaTC7wk1+3Y2hUd5QuMccWVozojfm6w==
  dependencies:
    "@satorijs/element" "^3.1.7"
    cosmokit "^1.7.2"
    schemastery-vue "^7.3.13"

"@koishijs/console@^5.19.1", "@koishijs/console@^5.20.0", "@koishijs/console@^5.24.3", "@koishijs/console@^5.25.6", "@koishijs/console@^5.25.8", "@koishijs/console@^5.29.2", "@koishijs/console@^5.30.0", "@koishijs/console@^5.30.2", "@koishijs/console@^5.30.7", "@koishijs/console@^5.30.8", "@koishijs/console@^5.30.9":
  version "5.30.9"
  resolved "https://registry.npmjs.org/@koishijs/console/-/console-5.30.9.tgz"
  integrity sha512-dJdZYtqsZcoFmsa1hVGmhg/hdIk+iL3d3yMfjnAjSsSy0yigFyXAJxQC1HNsn++vx+H7dRYGMbfFjelkpk7i/Q==

"@koishijs/core@4.18.8":
  version "4.18.8"
  resolved "https://registry.npmjs.org/@koishijs/core/-/core-4.18.8.tgz"
  integrity sha512-eeq4DxdTWjCR9Zfp74aXzk25CBw+PctnvX/2bFEGrhHdsIccOLrsYoBaWZcYeb4zhiRp9xGCOaQsEfeafzECGg==
  dependencies:
    "@koishijs/i18n-utils" "^1.0.1"
    "@koishijs/utils" "^7.2.1"
    "@satorijs/core" "^4.5.1"
    cordis "^3.18.1"
    cosmokit "^1.6.3"
    fastest-levenshtein "^1.0.16"
    minato "^3.6.1"

"@koishijs/i18n-utils@^1.0.1":
  version "1.0.1"
  resolved "https://registry.npmjs.org/@koishijs/i18n-utils/-/i18n-utils-1.0.1.tgz"
  integrity sha512-N58UAnhMMtqS4+VGW5IwJ6U9xwXZpzrcPaJrdEBzUJ4yBKKrkHfG1ck9tvQ99SnzxhZGGcBa/SKWnHQf/omrvg==
  dependencies:
    cosmokit "^1.5.2"

"@koishijs/loader@^4.5.3", "@koishijs/loader@^4.6.0", "@koishijs/loader@4.6.8":
  version "4.6.8"
  resolved "https://registry.npmjs.org/@koishijs/loader/-/loader-4.6.8.tgz"
  integrity sha512-EGPr239i9KVS50BDCrPDORG4XpbDYFIWAEv7/5Ue722lPkAR+M/Z86CrjhBqm32LhoimQo3R3ilCBoKCBVCMiw==
  dependencies:
    dotenv "^16.4.5"
    js-yaml "^4.1.0"
    ns-require "^1.1.4"

"@koishijs/plugin-actions@^0.0.2":
  version "0.0.2"
  resolved "https://registry.npmjs.org/@koishijs/plugin-actions/-/plugin-actions-0.0.2.tgz"
  integrity sha512-aYnj7An4vbN5Fbnj4Pm8MVjIT7KKwF7/k8Of7clqixuzZin0WtGAyDyWZ8+tQDQZ9MMea0dUDXvqtikIyXIZ6A==
  dependencies:
    "@koishijs/console" "^5.19.1"

"@koishijs/plugin-adapter-discord@^4.5.10":
  version "4.5.10"
  resolved "https://registry.npmjs.org/@koishijs/plugin-adapter-discord/-/plugin-adapter-discord-4.5.10.tgz"
  integrity sha512-/hZtop7Fzza/J9bSx1igzTzxvljfoZtyuX0B74mhw5XvnooTB6tUP9C2E2B42PSNOgOMitreHQNoFzGfIJ3QMw==
  dependencies:
    "@satorijs/adapter-discord" "^4.5.10"

"@koishijs/plugin-adapter-kook@^4.6.5":
  version "4.7.0"
  resolved "https://registry.npmjs.org/@koishijs/plugin-adapter-kook/-/plugin-adapter-kook-4.7.0.tgz"
  integrity sha512-GuYP1VuYGhkRpT3nRLlQMYTQMg8LjhgvP1Tb45vDiXxBpkS2C3FrVD4NWbW2IFO+Q2IUA04RnEYPEPzBqSmSsg==
  dependencies:
    "@satorijs/adapter-kook" "^4.7.0"

"@koishijs/plugin-adapter-qq@^4.9.2":
  version "4.10.1"
  resolved "https://registry.npmjs.org/@koishijs/plugin-adapter-qq/-/plugin-adapter-qq-4.10.1.tgz"
  integrity sha512-ZKC12iKwE8yRq0uqQMDGuEX+1SAzEeaasVd8GhI8WGSSFHIhzgeubBmYplUK8f7w9J0sjQ7spisP49P29+DTPw==
  dependencies:
    "@satorijs/adapter-qq" "^4.10.1"

"@koishijs/plugin-adapter-satori@^1.5.1":
  version "1.5.1"
  resolved "https://registry.npmjs.org/@koishijs/plugin-adapter-satori/-/plugin-adapter-satori-1.5.1.tgz"
  integrity sha512-V7hBu/JP5Ra+r5fw5863x6kEYQyyl97umgcn43Bj8cz9greP/sR9vefzJAjdW0DKxLo/NdJMGHCQ6LFp9XKu5g==
  dependencies:
    "@satorijs/adapter-satori" "^1.5.1"

"@koishijs/plugin-adapter-telegram@^4.5.8":
  version "4.5.10"
  resolved "https://registry.npmjs.org/@koishijs/plugin-adapter-telegram/-/plugin-adapter-telegram-4.5.10.tgz"
  integrity sha512-zXlcdonoRGyIgW5MQV2Ak9W4LZxC/nIn5El7oFrpSlXjSOX32BTeR10oTpAIuugnsulOmldOOZAX3OyUG40NcA==
  dependencies:
    "@satorijs/adapter-telegram" "^4.5.10"

"@koishijs/plugin-admin@^2.0.0-beta.4":
  version "2.0.0-beta.4"
  resolved "https://registry.npmjs.org/@koishijs/plugin-admin/-/plugin-admin-2.0.0-beta.4.tgz"
  integrity sha512-D9lIHWC0Mw3UFn521RQUYHZxHjR5HT0t4ZBQRvbWz/Lz+panSEgukWUkmrrwfnpXbVmzi1QiPO2me4V9WMS/Aw==
  dependencies:
    throttle-debounce "^3.0.1"

"@koishijs/plugin-analytics@^2.0.6":
  version "2.0.6"
  resolved "https://registry.npmjs.org/@koishijs/plugin-analytics/-/plugin-analytics-2.0.6.tgz"
  integrity sha512-SGZelLyWEQanIOnezhDgBKIZtKhwBvWry5CqSmGlFS/eM0hD0SKtS8fmrZAlKQ1ieBptLEJHpnHtl0fkQ7RRZA==
  dependencies:
    "@koishijs/console" "^5.24.3"

"@koishijs/plugin-auth@^4.1.6":
  version "4.1.6"
  resolved "https://registry.npmjs.org/@koishijs/plugin-auth/-/plugin-auth-4.1.6.tgz"
  integrity sha512-itXswJB9ChH2b6K3CK7Zpzr/1UFu/F8L5OJRaQdWI/IzO/JBnLeEGBe5mrZC1gEOEw1UXVPMW7X0Ep0cu8TqDg==
  dependencies:
    "@koishijs/console" "^5.20.0"

"@koishijs/plugin-bind@^1.5.1":
  version "1.5.1"
  resolved "https://registry.npmjs.org/@koishijs/plugin-bind/-/plugin-bind-1.5.1.tgz"
  integrity sha512-/8Ty4ST/1EghTKO3of1nmi0d46teHEZNHWrCRUK0shq6zw8QtN4++V+NogIZFtN64fss5c5zm6mzkan6uecVsw==

"@koishijs/plugin-commands@^3.5.4":
  version "3.5.5"
  resolved "https://registry.npmjs.org/@koishijs/plugin-commands/-/plugin-commands-3.5.5.tgz"
  integrity sha512-McpB64y8ItzHLwKwrWm9auaugVO2kSJ2qq7SCvApkP2khSqdTT6bQqv+uXKxpenG/WjJT/F6ZNKZb4xaSAMQww==
  dependencies:
    "@koishijs/console" "^5.30.8"

"@koishijs/plugin-config@^2.8.6":
  version "2.8.6"
  resolved "https://registry.npmjs.org/@koishijs/plugin-config/-/plugin-config-2.8.6.tgz"
  integrity sha512-5oEzC6DsJtr7Ze65FkiVkslIs0a7MSEDTNXI/OrNKXwA4kKV9Xso68Ky2CbHYl1w5TJyTzKGtvWan+THJhSaIg==
  dependencies:
    "@koishijs/console" "^5.30.0"
    "@koishijs/registry" "^7.0.3"

"@koishijs/plugin-console@^5.18.2", "@koishijs/plugin-console@^5.19.1", "@koishijs/plugin-console@^5.20.0", "@koishijs/plugin-console@^5.24.3", "@koishijs/plugin-console@^5.25.6", "@koishijs/plugin-console@^5.25.8", "@koishijs/plugin-console@^5.29.2", "@koishijs/plugin-console@^5.30.0", "@koishijs/plugin-console@^5.30.2", "@koishijs/plugin-console@^5.30.4", "@koishijs/plugin-console@^5.30.7", "@koishijs/plugin-console@^5.30.8":
  version "5.30.9"
  resolved "https://registry.npmjs.org/@koishijs/plugin-console/-/plugin-console-5.30.9.tgz"
  integrity sha512-cIio1XNVBi/aGluIVCLFShT87uv3G64HzUm8wcVPgFJj5YQ/BMQKxE4Iq+xJwkDFKt5LMnRXb/iWzn+Xsu413w==
  dependencies:
    "@koishijs/console" "^5.30.9"
    open "^8.4.2"
    uuid "^8.3.2"
    ws "^8.16.0"

"@koishijs/plugin-database-sqlite@^4.6.0":
  version "4.6.0"
  resolved "https://registry.npmjs.org/@koishijs/plugin-database-sqlite/-/plugin-database-sqlite-4.6.0.tgz"
  integrity sha512-lY4zHzBTpittdC7Ms8zhfRORzdoG6N5yKEORIMItod3XJbwJwkcrAZdmwQ9s2oKwjlT9dgF72ibKIqebegBzYg==
  dependencies:
    "@minatojs/driver-sqlite" "^4.6.0"

"@koishijs/plugin-explorer@^1.5.5":
  version "1.5.5"
  resolved "https://registry.npmjs.org/@koishijs/plugin-explorer/-/plugin-explorer-1.5.5.tgz"
  integrity sha512-Sfd2rf6SLzIiZemp+WwT1HmAytWvKIVV62lSNCrTOMG/yikXFuUe65r/uU3Y3/LjDTzpAxzC4dFCDjyg7TjygQ==
  dependencies:
    "@koishijs/console" "^5.29.2"
    anymatch "^3.1.3"
    chardet "^2.0.0"
    chokidar "^3.6.0"
    file-type "^16.5.4"
    throttle-debounce "^3.0.1"

"@koishijs/plugin-help@^2.4.5":
  version "2.4.5"
  resolved "https://registry.npmjs.org/@koishijs/plugin-help/-/plugin-help-2.4.5.tgz"
  integrity sha512-O/2u8GAEOYMvk1qMK877V3oS8e5AIZHffUSomwjCDK9x/R6wZ5U7pelI2NnchJSibqYXeOSwgP4XkktGlk5oxg==

"@koishijs/plugin-hmr@^1.2.9":
  version "1.2.9"
  resolved "https://registry.npmjs.org/@koishijs/plugin-hmr/-/plugin-hmr-1.2.9.tgz"
  integrity sha512-Xlz81Ek9Eu0P71A9bu3f6gDwevoEjEeb2/SL0agCGQc+cA1wwk6mEfoJiBlqWz1VeL4vIzuc7Rph4fNaJCz2/A==
  dependencies:
    "@babel/code-frame" "^7.24.2"
    chokidar "^3.6.0"

"@koishijs/plugin-http@^0.6.3":
  version "0.6.3"
  resolved "https://registry.npmjs.org/@koishijs/plugin-http/-/plugin-http-0.6.3.tgz"
  integrity sha512-alw51yjc6bykgZD2EsbTn03Yjwp9Mkidb1BsQn25SDwaBQqla0bZAVivFxNW1J60lneP9uymu3EUnPspvBuxog==
  dependencies:
    "@cordisjs/plugin-http" "^0.6.3"

"@koishijs/plugin-insight@^3.5.2":
  version "3.5.2"
  resolved "https://registry.npmjs.org/@koishijs/plugin-insight/-/plugin-insight-3.5.2.tgz"
  integrity sha512-o354/sZ6nMMrqv1cmFG9R81ktBV1oU2zAZoS3O29Ie64V1HjCQ0AZcLm2ROpxNLa5pB2Xxyzh7PD1ZnNuywGpQ==
  dependencies:
    "@koishijs/console" "^5.29.2"

"@koishijs/plugin-inspect@^1.1.7":
  version "1.1.7"
  resolved "https://registry.npmjs.org/@koishijs/plugin-inspect/-/plugin-inspect-1.1.7.tgz"
  integrity sha512-B9uQ2fhAl5IGbVhd6zq36Hb153pIhcc/eH+2xSJVj5FZYHtn49eh6jeywNo1HGepaT+8iiiZM58ZgPQewsaryg==

"@koishijs/plugin-locales@^2.5.3":
  version "2.5.3"
  resolved "https://registry.npmjs.org/@koishijs/plugin-locales/-/plugin-locales-2.5.3.tgz"
  integrity sha512-lt0749Idc7wLAV6PjTYniZXvbTb2y7wpnTJjhiKHFKG6ywA1PTXbBnntqDqWv/bL9HTX36tG3gw/Rq8yrketHQ==
  dependencies:
    "@koishijs/console" "^5.25.6"
    js-yaml "^4.1.0"

"@koishijs/plugin-logger@^2.6.9":
  version "2.6.9"
  resolved "https://registry.npmjs.org/@koishijs/plugin-logger/-/plugin-logger-2.6.9.tgz"
  integrity sha512-5+xkT67symDXnwC+i8ZFKL+**************************+FbeulCScZgMDpGOiaxSCGIjjUorAln8Q94/g==
  dependencies:
    "@koishijs/console" "^5.30.2"

"@koishijs/plugin-market@^2.11.4":
  version "2.11.6"
  resolved "https://registry.npmjs.org/@koishijs/plugin-market/-/plugin-market-2.11.6.tgz"
  integrity sha512-Bgl0C72Y8p9OJgRLV/PoCxijQ2wnLWN1mFOA7E5/9sBlTNAdDq2TaxMgF5Bbx4AqeUGNte6bJf12E+UAsjYH0w==
  dependencies:
    "@koishijs/console" "^5.30.7"
    "@koishijs/registry" "^7.0.3"
    execa "^5.1.1"
    get-registry "^1.2.0"
    ns-require "^1.1.4"
    p-map "^4.0.0"
    semver "^7.6.3"
    which-pm-runs "^1.1.0"

"@koishijs/plugin-notifier@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npmjs.org/@koishijs/plugin-notifier/-/plugin-notifier-1.2.1.tgz"
  integrity sha512-RF+dJkOIiTQeNNjygBx0Unzdnmio+fmmqYKZHIZJMehEv4ktGc74Q1fAbWJBk60W34sS4rDhxkmrnxBwT0/RYw==

"@koishijs/plugin-oobe@^0.0.2":
  version "0.0.2"
  resolved "https://registry.npmjs.org/@koishijs/plugin-oobe/-/plugin-oobe-0.0.2.tgz"
  integrity sha512-p21xtrZlJOWdS5kfxsZRj+ViOCGPkHMonVRkNG3QphQTpkqxgNet2ncFILo06L5xY9OtNVIsGRVqs/XMJgyW0Q==
  dependencies:
    "@koishijs/console" "^5.19.1"

"@koishijs/plugin-proxy-agent@^0.3.3":
  version "0.3.3"
  resolved "https://registry.npmjs.org/@koishijs/plugin-proxy-agent/-/plugin-proxy-agent-0.3.3.tgz"
  integrity sha512-pOpsHNVn3RVpPwdQjHNBRFsuq02sAS5NRVSuWWSHBPwIQ+QWu5232Au9RVH/DHBvx6XLWOm+WePMo4gRaxsVgg==
  dependencies:
    "@cordisjs/plugin-proxy-agent" "^0.3.3"

"@koishijs/plugin-sandbox@^3.4.1":
  version "3.4.2"
  resolved "https://registry.npmjs.org/@koishijs/plugin-sandbox/-/plugin-sandbox-3.4.2.tgz"
  integrity sha512-uD5k42g/l/N4ByzErykIWOuxcWMMjzTn2BDiitDkGm6Q9uPtrDnqnQ2e6eN6+w7R1j4a8D4jKJipkCu9CapeGQ==
  dependencies:
    "@koishijs/console" "^5.30.7"

"@koishijs/plugin-server-satori@^2.9.0":
  version "2.9.0"
  resolved "https://registry.npmjs.org/@koishijs/plugin-server-satori/-/plugin-server-satori-2.9.0.tgz"
  integrity sha512-6+fZs7BpdsXxpVZNGMaasEEPabF8W16/KppD/lopun7fKXOd5l1pSAWqZBXdbJ1p7WarfA1O3nS7lRTtUrzzZA==
  dependencies:
    "@satorijs/plugin-server" "^2.9.0"

"@koishijs/plugin-server-temp@^1.5.0":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@koishijs/plugin-server-temp/-/plugin-server-temp-1.5.0.tgz"
  integrity sha512-NuXdaWDa1m0/BfaEoPJN7j0E4PYe2rvg2WUqLOE5yocF/UC3kRuuz5T30pMfCUab/WskyB2gydrygcdGsJXlGA==
  dependencies:
    "@cordisjs/plugin-server-temp" "^0.5.0"

"@koishijs/plugin-server@^3.2.4", "@koishijs/plugin-server@^3.2.7":
  version "3.2.7"
  resolved "https://registry.npmjs.org/@koishijs/plugin-server/-/plugin-server-3.2.7.tgz"
  integrity sha512-JgOwu6orHSfVMNiBa+ItUkYzEl8aV4gfmobLYRljHAw9E9AGZiAdwV9Nzwow0JxCSpLlW5sNDoCNfBohfOc24Q==
  dependencies:
    "@cordisjs/plugin-server" "^0.2.7"

"@koishijs/plugin-status@^7.4.10":
  version "7.4.10"
  resolved "https://registry.npmjs.org/@koishijs/plugin-status/-/plugin-status-7.4.10.tgz"
  integrity sha512-mtnrc90jZuM0hpjqHWOD9X1+RB1KK5Nrx7TvF8/VLk5HXR9djeFnE1VyPuBWg5SivpOvCBSHDy6Vtza+qN0xaw==
  dependencies:
    "@koishijs/console" "^5.25.8"
    envinfo "^7.11.0"
    which-pm-runs "^1.1.0"

"@koishijs/registry@^7.0.3":
  version "7.0.3"
  resolved "https://registry.npmjs.org/@koishijs/registry/-/registry-7.0.3.tgz"
  integrity sha512-Ze6cxfl1TlVFIdb6whWcdI5UT6xxeF5k1/RMUWM31gcegR09KtXGgky7N0gJRMcNByrx9o9VZyZu641jAhQNbQ==
  dependencies:
    cosmokit "^1.5.2"
    p-map "^4.0.0"
    semver "^7.5.4"

"@koishijs/scripts@^4.6.1":
  version "4.6.1"
  resolved "https://registry.npmjs.org/@koishijs/scripts/-/scripts-4.6.1.tgz"
  integrity sha512-y+VtlUsxq8VygY4I3UZuxR4d1XRrea9flth1i9rZdcnLvkZd8+aCkTIWbD1DC6QwjKmeThMYmwbjp/gqGAZnhQ==
  dependencies:
    cac "^6.7.14"
    fs-extra "^10.1.0"
    globby "^11.1.0"
    js-yaml "^4.1.0"
    kleur "^4.1.5"
    prompts "^2.4.2"
    which-pm-runs "^1.1.0"

"@koishijs/utils@^7.2.1":
  version "7.3.2"
  resolved "https://registry.npmjs.org/@koishijs/utils/-/utils-7.3.2.tgz"
  integrity sha512-x9kEB/nM6l+Uqtnaqqf/FyuOxcT7snp+zu0oTEgV8Z7aZ7eBfuf3Ac1CTAlaiyGPW1aOgXMUg7WL44yVIfRlOw==
  dependencies:
    cosmokit "^1.7.2"
    inaba "^1.1.1"

"@maikolib/vite-plugin-yaml@^1.0.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@maikolib/vite-plugin-yaml/-/vite-plugin-yaml-1.1.1.tgz"
  integrity sha512-+MLv4kJ1lYq+iXT9PN6FGxoE8apv7CSsNVrntXiXJ2DtcUpGpApO35/qtAyMXRJmNuRKCx0rOdC/MUkrmojOHw==
  dependencies:
    "@rollup/pluginutils" "5.1.0"
    js-yaml "4.1.0"
    tosource "2.0.0-alpha.3"

"@minatojs/driver-sqlite@^4.6.0":
  version "4.6.0"
  resolved "https://registry.npmjs.org/@minatojs/driver-sqlite/-/driver-sqlite-4.6.0.tgz"
  integrity sha512-8o1GUjfOjSKVvyJxPZyxg/0M4VE8CmIlxBKb43+BGGWDfqFFaWky6JDny/GwTLKHGtDV9FCOrwkACfrWBtg3Lw==
  dependencies:
    "@minatojs/sql-utils" "^5.5.0"
    "@minatojs/sql.js" "^3.1.0"
    cosmokit "^1.6.2"

"@minatojs/sql-utils@^5.5.0":
  version "5.5.0"
  resolved "https://registry.npmjs.org/@minatojs/sql-utils/-/sql-utils-5.5.0.tgz"
  integrity sha512-mukqSsv/nYSPGYE+iWg7UJx5LcNFCofziyQeGRGPz6ZJWCu674zHx1r7cDl5iYtG94eXJQb5WiOgT7EJdGElqw==
  dependencies:
    cosmokit "^1.6.2"

"@minatojs/sql.js@^3.1.0":
  version "3.1.0"
  resolved "https://registry.npmjs.org/@minatojs/sql.js/-/sql.js-3.1.0.tgz"
  integrity sha512-jaEFlM03k+maWc4JHv5DgEPq6r5bcg5OmXUZ6klWbYUi7tfRySGxLjwh70v4+p8A9qxdyL9yX3D2YQiXwl1HKw==

"@noble/hashes@^1.1.5":
  version "1.8.0"
  resolved "https://registry.npmjs.org/@noble/hashes/-/hashes-1.8.0.tgz"
  integrity sha512-jCs9ldd7NwzpgXDIf6P3+NrHh9/sD6CQdxHyjQI+h/6rDNo88ypBxxz45UDuZHz9r3tNz7N/VInSVoVdtXEI4A==

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@paralleldrive/cuid2@^2.2.2":
  version "2.2.2"
  resolved "https://registry.npmjs.org/@paralleldrive/cuid2/-/cuid2-2.2.2.tgz"
  integrity sha512-ZOBkgDwEdoYVlSeRbYYXs0S9MejQofiVYoTbKzy/6GQa39/q5tQU2IX46+shYnUkpEl3wc+J6wRlar7r2EK2xA==
  dependencies:
    "@noble/hashes" "^1.1.5"

"@parcel/watcher-win32-x64@2.5.1":
  version "2.5.1"
  resolved "https://registry.npmjs.org/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.5.1.tgz"
  integrity sha512-9lHBdJITeNR++EvSQVUcaZoWupyHfXe1jZvGZ06O/5MflPcuPLtEphScIBL+AiCWBO46tDSHzWyD0uDmmZqsgA==

"@parcel/watcher@^2.4.1":
  version "2.5.1"
  resolved "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.5.1.tgz"
  integrity sha512-dfUnCxiN9H4ap84DvD2ubjw+3vUNpstxa0TneY/Paat8a3R4uQZDLSvWjmznAY/DoahqTHl9V46HF/Zs3F29pg==
  dependencies:
    detect-libc "^1.0.3"
    is-glob "^4.0.3"
    micromatch "^4.0.5"
    node-addon-api "^7.0.0"
  optionalDependencies:
    "@parcel/watcher-android-arm64" "2.5.1"
    "@parcel/watcher-darwin-arm64" "2.5.1"
    "@parcel/watcher-darwin-x64" "2.5.1"
    "@parcel/watcher-freebsd-x64" "2.5.1"
    "@parcel/watcher-linux-arm-glibc" "2.5.1"
    "@parcel/watcher-linux-arm-musl" "2.5.1"
    "@parcel/watcher-linux-arm64-glibc" "2.5.1"
    "@parcel/watcher-linux-arm64-musl" "2.5.1"
    "@parcel/watcher-linux-x64-glibc" "2.5.1"
    "@parcel/watcher-linux-x64-musl" "2.5.1"
    "@parcel/watcher-win32-arm64" "2.5.1"
    "@parcel/watcher-win32-ia32" "2.5.1"
    "@parcel/watcher-win32-x64" "2.5.1"

"@polka/url@^1.0.0-next.24":
  version "1.0.0-next.29"
  resolved "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.29.tgz"
  integrity sha512-wwQAWhWSuHaag8c4q/KN/vCoeOJYshAIvMQwD4GpSb3OiZklFfvAgmj0VCBBImRpuF/aFgIRzllXlVX93Jevww==

"@popperjs/core@npm:@sxzz/popperjs-es@^2.11.7":
  version "2.11.7"
  resolved "https://registry.npmjs.org/@sxzz/popperjs-es/-/popperjs-es-2.11.7.tgz"
  integrity sha512-Ccy0NlLkzr0Ex2FKvh2X+OyERHXJ88XJ1MXtsI9y9fGexlaXaVTPzBCRBwIxFkORuOb+uBqeu+RqnpgYTEZRUQ==

"@puppeteer/browsers@2.3.0":
  version "2.3.0"
  resolved "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.3.0.tgz"
  integrity sha512-ioXoq9gPxkss4MYhD+SFaU9p1IHFUX0ILAWFPyjGaBdjLsYAlZw6j1iLA0N/m12uVHLFDfSYNF7EQccjinIMDA==
  dependencies:
    debug "^4.3.5"
    extract-zip "^2.0.1"
    progress "^2.0.3"
    proxy-agent "^6.4.0"
    semver "^7.6.3"
    tar-fs "^3.0.6"
    unbzip2-stream "^1.4.3"
    yargs "^17.7.2"

"@rollup/pluginutils@^5.1.4":
  version "5.2.0"
  resolved "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-5.2.0.tgz"
  integrity sha512-qWJ2ZTbmumwiLFomfzTyt5Kng4hwPi9rwCYN4SHb6eaRU1KNO4ccxINHr/VhH4GgPlt1XfSTLX2LBTme8ne4Zw==
  dependencies:
    "@types/estree" "^1.0.0"
    estree-walker "^2.0.2"
    picomatch "^4.0.2"

"@rollup/pluginutils@5.1.0":
  version "5.1.0"
  resolved "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-5.1.0.tgz"
  integrity sha512-XTIWOPPcpvyKI6L1NHo0lFlCyznUEyPmPY1mc3KpPVDYulHSTvyeLNVW00QTLIAFNhR3kYnJTQHeGqU4M3n09g==
  dependencies:
    "@types/estree" "^1.0.0"
    estree-walker "^2.0.2"
    picomatch "^2.3.1"

"@rollup/rollup-win32-x64-msvc@4.44.1":
  version "4.44.1"
  resolved "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.44.1.tgz"
  integrity sha512-J8o22LuF0kTe7m+8PvW9wk3/bRq5+mRo5Dqo6+vXb7otCm3TPhYOJqOaQtGU9YMWQSL3krMnoOxMr0+9E6F3Ug==

"@satorijs/adapter-discord@^4.5.10":
  version "4.5.10"
  resolved "https://registry.npmjs.org/@satorijs/adapter-discord/-/adapter-discord-4.5.10.tgz"
  integrity sha512-ac5Rt9b9bzKjx6+MNN+78Hwbw7+Jc/kal1cvR2BFnMCQFo7QwPx0yr5HSusTBe0gYree5KXMRPaAvfiA0CMGLg==

"@satorijs/adapter-kook@^4.7.0":
  version "4.7.0"
  resolved "https://registry.npmjs.org/@satorijs/adapter-kook/-/adapter-kook-4.7.0.tgz"
  integrity sha512-1kn5IeBwqEi8fYIWmGvYZpTaLuXg1R+j9eqP1TvTSSd/Oq/9eqjKm5Gs8fCuUdTEmtIJ6JE0mUZS1DRVsX3UzA==

"@satorijs/adapter-qq@^4.10.1":
  version "4.10.1"
  resolved "https://registry.npmjs.org/@satorijs/adapter-qq/-/adapter-qq-4.10.1.tgz"
  integrity sha512-2se6B2PwsJNtLWOUKXZcyXTowtWD9v5EkxYrIrSy3NOoRvxFqR0c9UMOILX33XFItLvV2mcOnsDtojhpfKFdjg==

"@satorijs/adapter-satori@^1.5.1":
  version "1.5.1"
  resolved "https://registry.npmjs.org/@satorijs/adapter-satori/-/adapter-satori-1.5.1.tgz"
  integrity sha512-CkW6A0l0icyYhIduR853QhMi6Ifb6lUfIrHOfZ9v3+IOPlnV9WrJv9AQ6gD7jMTUvTys+rCZlUChy62AX8CGRg==

"@satorijs/adapter-telegram@^4.5.10":
  version "4.5.10"
  resolved "https://registry.npmjs.org/@satorijs/adapter-telegram/-/adapter-telegram-4.5.10.tgz"
  integrity sha512-3a4VZH2wZz6YXwLvWP/fiXhOvvmlwLqrtoQPrLPi8pO/nm2tC6g5BVi5SPn1uIEtq8trf2cgDxLaWCkFpU0Sfg==
  dependencies:
    file-type "^16.5.4"

"@satorijs/core@^4.3.3", "@satorijs/core@^4.5.0", "@satorijs/core@^4.5.1", "@satorijs/core@^4.5.2":
  version "4.5.2"
  resolved "https://registry.npmjs.org/@satorijs/core/-/core-4.5.2.tgz"
  integrity sha512-lW5gqt6z1h56GGkyXKEFn212JstDcVN2rUxb+FBvT9i6OKLdB2055QgynrkbLqzDN+Zq8hbW+9T6lpS/fpyMSw==
  dependencies:
    "@cordisjs/plugin-http" "^0.6.3"
    "@satorijs/element" "^3.1.8"
    "@satorijs/protocol" "^1.6.1"
    cordis "^3.18.1"
    cosmokit "^1.7.2"
    path-to-regexp "^8.2.0"

"@satorijs/element@^3.1.7", "@satorijs/element@^3.1.8":
  version "3.1.8"
  resolved "https://registry.npmjs.org/@satorijs/element/-/element-3.1.8.tgz"
  integrity sha512-Sm+YCO0GMKDtAsQ+JKZ6aJeJZYVeMehqmzk4EJr57MnvZt5kP9iqtN8vYOZc/idE69Y5nA/jxKNCSrgrm1yYrA==
  dependencies:
    cosmokit "^1.7.2"

"@satorijs/plugin-server@^2.9.0":
  version "2.9.0"
  resolved "https://registry.npmjs.org/@satorijs/plugin-server/-/plugin-server-2.9.0.tgz"
  integrity sha512-l5f2UKISjOnvm69we08KIh/NRXeT/V0pd7cI6Cugm62GpBYcMILA8Fv24rdcCOJ7RhOL9dqW7soTtjXBb2z6AA==
  dependencies:
    cosmokit "^1.6.3"

"@satorijs/protocol@^1.6.1":
  version "1.6.1"
  resolved "https://registry.npmjs.org/@satorijs/protocol/-/protocol-1.6.1.tgz"
  integrity sha512-A/G/S5PWXGXg9B4aMAmvt8+wP2BnV6uNEzkd1MH1ZkqWfgJ2TX9L4ghR311mbde57MJOoSbX51lnO4J9XjeNKQ==
  dependencies:
    "@satorijs/element" "^3.1.8"
    cosmokit "^1.7.2"

"@tokenizer/token@^0.3.0":
  version "0.3.0"
  resolved "https://registry.npmjs.org/@tokenizer/token/-/token-0.3.0.tgz"
  integrity sha512-OvjF+z51L3ov0OyAU0duzsYuvO01PH7x4t6DJx+guahgTnBHkhJdG7soQeTSFLWN3efnHyibZ4Z8l2EuWwJN3A==

"@tootallnate/quickjs-emscripten@^0.23.0":
  version "0.23.0"
  resolved "https://registry.npmjs.org/@tootallnate/quickjs-emscripten/-/quickjs-emscripten-0.23.0.tgz"
  integrity sha512-C5Mc6rdnsaJDjO3UpGW/CQTHtCKaYlScZTly4JIu97Jxo/odCiH0ITnDXSJPTOrEKk/ycSZ0AOgTmkDtkOsvIA==

"@types/accepts@*":
  version "1.3.7"
  resolved "https://registry.npmjs.org/@types/accepts/-/accepts-1.3.7.tgz"
  integrity sha512-Pay9fq2lM2wXPWbteBsRAGiWH2hig4ZE2asK+mm7kUzlxRTfL961rj89I6zV/E3PcIkDqyuBEcMxFT7rccugeQ==
  dependencies:
    "@types/node" "*"

"@types/body-parser@*":
  version "1.19.6"
  resolved "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.6.tgz"
  integrity sha512-HLFeCYgz89uk22N5Qg3dvGvsv46B8GLvKKo1zKG4NybA8U2DiEO3w9lqGg29t/tfLRJpJ6iQxnVw4OnB7MoM9g==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/co-body@^6.1.0":
  version "6.1.3"
  resolved "https://registry.npmjs.org/@types/co-body/-/co-body-6.1.3.tgz"
  integrity sha512-UhuhrQ5hclX6UJctv5m4Rfp52AfG9o9+d9/HwjxhVB5NjXxr5t9oKgJxN8xRHgr35oo8meUEHUPFWiKg6y71aA==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz"
  integrity sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==
  dependencies:
    "@types/node" "*"

"@types/content-disposition@*":
  version "0.5.9"
  resolved "https://registry.npmjs.org/@types/content-disposition/-/content-disposition-0.5.9.tgz"
  integrity sha512-8uYXI3Gw35MhiVYhG3s295oihrxRyytcRHjSjqnqZVDDy/xcGBRny7+Xj1Wgfhv5QzRtN2hB2dVRBUX9XW3UcQ==

"@types/cookies@*":
  version "0.9.1"
  resolved "https://registry.npmjs.org/@types/cookies/-/cookies-0.9.1.tgz"
  integrity sha512-E/DPgzifH4sM1UMadJMWd6mO2jOd4g1Ejwzx8/uRCDpJis1IrlyQEcGAYEomtAqRYmD5ORbNXMeI9U0RiVGZbg==
  dependencies:
    "@types/connect" "*"
    "@types/express" "*"
    "@types/keygrip" "*"
    "@types/node" "*"

"@types/estree@^1.0.0", "@types/estree@1.0.8":
  version "1.0.8"
  resolved "https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz"
  integrity sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==

"@types/express-serve-static-core@^5.0.0":
  version "5.0.6"
  resolved "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.6.tgz"
  integrity sha512-3xhRnjJPkULekpSzgtoNYYcTWgEZkp4myc+Saevii5JPnHNvHMRlBSHDbs7Bh1iPPoVTERHEZXyhyLbMEsExsA==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*":
  version "5.0.3"
  resolved "https://registry.npmjs.org/@types/express/-/express-5.0.3.tgz"
  integrity sha512-wGA0NX93b19/dZC1J18tKWVIYWyyF2ZjT9vin/NRu0qzzvfVzWjs04iq2rQ3H65vCTQYlRqs3YHfY7zjdV+9Kw==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^5.0.0"
    "@types/serve-static" "*"

"@types/formidable@^2.0.5":
  version "2.0.6"
  resolved "https://registry.npmjs.org/@types/formidable/-/formidable-2.0.6.tgz"
  integrity sha512-L4HcrA05IgQyNYJj6kItuIkXrInJvsXTPC5B1i64FggWKKqSL+4hgt7asiSNva75AoLQjq29oPxFfU4GAQ6Z2w==
  dependencies:
    "@types/node" "*"

"@types/http-assert@*":
  version "1.5.6"
  resolved "https://registry.npmjs.org/@types/http-assert/-/http-assert-1.5.6.tgz"
  integrity sha512-TTEwmtjgVbYAzZYWyeHPrrtWnfVkm8tQkP8P21uQifPgMRgjrow3XDEYqucuC8SKZJT7pUnhU/JymvjggxO9vw==

"@types/http-errors@*":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.5.tgz"
  integrity sha512-r8Tayk8HJnX0FztbZN7oVqGccWgw98T/0neJphO91KkmOzug1KkofZURD4UaD5uH8AqcFLfdPErnBod0u71/qg==

"@types/keygrip@*":
  version "1.0.6"
  resolved "https://registry.npmjs.org/@types/keygrip/-/keygrip-1.0.6.tgz"
  integrity sha512-lZuNAY9xeJt7Bx4t4dx0rYCDqGPW8RXhQZK1td7d4H6E9zYbLoOtjBvfwdTKpsyxQI/2jv+armjX/RW+ZNpXOQ==

"@types/koa__router@*":
  version "12.0.4"
  resolved "https://registry.npmjs.org/@types/koa__router/-/koa__router-12.0.4.tgz"
  integrity sha512-Y7YBbSmfXZpa/m5UGGzb7XadJIRBRnwNY9cdAojZGp65Cpe5MAP3mOZE7e3bImt8dfKS4UFcR16SLH8L/z7PBw==
  dependencies:
    "@types/koa" "*"

"@types/koa-compose@*":
  version "3.2.8"
  resolved "https://registry.npmjs.org/@types/koa-compose/-/koa-compose-3.2.8.tgz"
  integrity sha512-4Olc63RY+MKvxMwVknCUDhRQX1pFQoBZ/lXcRLP69PQkEpze/0cr8LNqJQe5NFb/b19DWi2a5bTi2VAlQzhJuA==
  dependencies:
    "@types/koa" "*"

"@types/koa@*", "@types/koa@^2.13.5":
  version "2.15.0"
  resolved "https://registry.npmjs.org/@types/koa/-/koa-2.15.0.tgz"
  integrity sha512-7QFsywoE5URbuVnG3loe03QXuGajrnotr3gQkXcEBShORai23MePfFYdhz90FEtBBpkyIYQbVD+evKtloCgX3g==
  dependencies:
    "@types/accepts" "*"
    "@types/content-disposition" "*"
    "@types/cookies" "*"
    "@types/http-assert" "*"
    "@types/http-errors" "*"
    "@types/keygrip" "*"
    "@types/koa-compose" "*"
    "@types/node" "*"

"@types/lodash-es@*", "@types/lodash-es@^4.17.6":
  version "4.17.12"
  resolved "https://registry.npmjs.org/@types/lodash-es/-/lodash-es-4.17.12.tgz"
  integrity sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ==
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*", "@types/lodash@^4.14.182":
  version "4.17.20"
  resolved "https://registry.npmjs.org/@types/lodash/-/lodash-4.17.20.tgz"
  integrity sha512-H3MHACvFUEiujabxhaI/ImO6gUrd8oOurg7LQtS7mbwIXA/cUqWrvBsaeJ23aZEPk1TAYkurjfMbSELfoCXlGA==

"@types/mime@^1":
  version "1.3.5"
  resolved "https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz"
  integrity sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==

"@types/node@*", "@types/node@^18.0.0 || >=20.0.0", "@types/node@^22.7.5":
  version "22.16.0"
  resolved "https://registry.npmjs.org/@types/node/-/node-22.16.0.tgz"
  integrity sha512-B2egV9wALML1JCpv3VQoQ+yesQKAmNMBIAY7OteVrikcOcAkWm+dGL6qpeCktPjAv6N1JLnhbNiqS35UpFyBsQ==
  dependencies:
    undici-types "~6.21.0"

"@types/prop-types@*":
  version "15.7.15"
  resolved "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.15.tgz"
  integrity sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==

"@types/qs@*":
  version "6.14.0"
  resolved "https://registry.npmjs.org/@types/qs/-/qs-6.14.0.tgz"
  integrity sha512-eOunJqu0K1923aExK6y8p6fsihYEn/BYuQ4g0CxAAgFc4b/ZLN4CrsRZ55srTdqoiLzU2B2evC+apEIxprEzkQ==

"@types/range-parser@*":
  version "1.2.7"
  resolved "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz"
  integrity sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==

"@types/react@^18.2.70":
  version "18.3.23"
  resolved "https://registry.npmjs.org/@types/react/-/react-18.3.23.tgz"
  integrity sha512-/LDXMQh55EzZQ0uVAZmKKhfENivEvWz6E+EYzh+/MCjMhNsotd+ZHhBGIjFDTi6+fz0OhQQQLbTgdQIxxCsC0w==
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/send@*":
  version "0.17.5"
  resolved "https://registry.npmjs.org/@types/send/-/send-0.17.5.tgz"
  integrity sha512-z6F2D3cOStZvuk2SaP6YrwkNO65iTZcwA2ZkSABegdkAh/lf+Aa/YQndZVfmEXT5vgAp6zv06VQ3ejSVjAny4w==
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-static@*":
  version "1.15.8"
  resolved "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.8.tgz"
  integrity sha512-roei0UY3LhpOJvjbIP6ZZFngyLKl5dskOtDhxY5THRSpO+ZI+nzJ+m5yUMzGrp89YRa7lvknKkMYjqQFGwA7Sg==
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

"@types/web-bluetooth@^0.0.16":
  version "0.0.16"
  resolved "https://registry.npmjs.org/@types/web-bluetooth/-/web-bluetooth-0.0.16.tgz"
  integrity sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ==

"@types/web-bluetooth@^0.0.20":
  version "0.0.20"
  resolved "https://registry.npmjs.org/@types/web-bluetooth/-/web-bluetooth-0.0.20.tgz"
  integrity sha512-g9gZnnXVq7gM7v3tJCWV/qw7w+KeOlSHAhgF9RytFyifW6AF61hdT2ucrYhPq9hLs5JIryeupHV3qGk95dH9ow==

"@types/ws@^8.5.10":
  version "8.18.1"
  resolved "https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz"
  integrity sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==
  dependencies:
    "@types/node" "*"

"@types/yauzl@^2.9.1":
  version "2.10.3"
  resolved "https://registry.npmjs.org/@types/yauzl/-/yauzl-2.10.3.tgz"
  integrity sha512-oJoftv0LSuaDZE3Le4DbKX+KS9G36NzOeSap90UIK0yMA/NhKJhqlSGtNDORNRaIbQfzjXDrQa0ytJ6mNRGz/Q==
  dependencies:
    "@types/node" "*"

"@unocss/astro@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/astro/-/astro-0.65.4.tgz"
  integrity sha512-ex1CJOQ6yeftBEPcbA9/W47/YoV+mhQnrAoc8MA1VVrvvFKDitICFU62+nSt3NWRe53XL/fXnQbcbCb8AAgKlA==
  dependencies:
    "@unocss/core" "0.65.4"
    "@unocss/reset" "0.65.4"
    "@unocss/vite" "0.65.4"

"@unocss/cli@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/cli/-/cli-0.65.4.tgz"
  integrity sha512-D/4hY5Hezh3QETscl4i+ojb+q8YU9Cl9AYJ8v3gsjc/GjTmEuIOD5V4x+/aN25vY5wjqgoApOgaIDGCV3b+2Ig==
  dependencies:
    "@ampproject/remapping" "^2.3.0"
    "@rollup/pluginutils" "^5.1.4"
    "@unocss/config" "0.65.4"
    "@unocss/core" "0.65.4"
    "@unocss/preset-uno" "0.65.4"
    cac "^6.7.14"
    chokidar "^3.6.0"
    colorette "^2.0.20"
    consola "^3.3.1"
    magic-string "^0.30.17"
    pathe "^1.1.2"
    perfect-debounce "^1.0.0"
    tinyglobby "^0.2.10"

"@unocss/config@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/config/-/config-0.65.4.tgz"
  integrity sha512-/vCt4AXnJ4p4Ow6xqsYwdrelF9533yhZjzkg3SQmL3rKeSkicPayKpeq8nkYECdhDI03VTCVD+6oh5Y/26Hg7A==
  dependencies:
    "@unocss/core" "0.65.4"
    unconfig "~0.6.0"

"@unocss/core@^0.65.4", "@unocss/core@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/core/-/core-0.65.4.tgz"
  integrity sha512-a2JOoFutrhqd5RgPhIR5FIXrDoHDU3gwCbPrpT6KYTjsqlSc/fv02yZ+JGOZFN3MCFhCmaPTs+idDFtwb3xU8g==

"@unocss/extractor-arbitrary-variants@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/extractor-arbitrary-variants/-/extractor-arbitrary-variants-0.65.4.tgz"
  integrity sha512-GbvTgsDaHplfWfsQtOY8RrvEZvptmvR9k9NwQ5NsZBNIG1JepYVel93CVQvsxT5KioKcoWngXxTYLNOGyxLs0g==
  dependencies:
    "@unocss/core" "0.65.4"

"@unocss/inspector@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/inspector/-/inspector-0.65.4.tgz"
  integrity sha512-byg9x549Ul17U4Ety7ufDwC0UOygypoq4QnLEPzhlZ0KJG1f7WmXKYanOhupeg3h4qCj6Nc/xdZYMGbHl9QRIg==
  dependencies:
    "@unocss/core" "0.65.4"
    "@unocss/rule-utils" "0.65.4"
    colorette "^2.0.20"
    gzip-size "^6.0.0"
    sirv "^3.0.0"
    vue-flow-layout "^0.1.1"

"@unocss/postcss@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/postcss/-/postcss-0.65.4.tgz"
  integrity sha512-8peDRo0+rNQsnKh/H2uZEVy67sV2cC16rAeSLpgbVJUMNfZlmF0rC2DNGsOV17uconUXSwz7+mGcHKNiv+8YlQ==
  dependencies:
    "@unocss/config" "0.65.4"
    "@unocss/core" "0.65.4"
    "@unocss/rule-utils" "0.65.4"
    css-tree "^3.1.0"
    postcss "^8.4.49"
    tinyglobby "^0.2.10"

"@unocss/preset-attributify@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/preset-attributify/-/preset-attributify-0.65.4.tgz"
  integrity sha512-zxE9hJJ5b37phjdzDdZsxX559ZlmH9rFlY5LVEcQySTnsfY0znviHxPbD2iRpCBCRd+YC5HfFd2jb3XlnTKMJQ==
  dependencies:
    "@unocss/core" "0.65.4"

"@unocss/preset-icons@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/preset-icons/-/preset-icons-0.65.4.tgz"
  integrity sha512-5sSzTN72X2Ag3VH48xY1pYudeWnql9jqdMiwgZuLJcmvETBNGelXy2wGxm7tsUUEx/l40Yr04Ck8XRPGT9jLBw==
  dependencies:
    "@iconify/utils" "^2.2.1"
    "@unocss/core" "0.65.4"
    ofetch "^1.4.1"

"@unocss/preset-mini@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/preset-mini/-/preset-mini-0.65.4.tgz"
  integrity sha512-dcO2PzSl87qN1KdQWcfZDIKEhpdFeImWbYfiXtE7k6pi1393FJkdHEopgI/1ZciIQN1CkTvQJ5c7EpEVWftYRA==
  dependencies:
    "@unocss/core" "0.65.4"
    "@unocss/extractor-arbitrary-variants" "0.65.4"
    "@unocss/rule-utils" "0.65.4"

"@unocss/preset-tagify@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/preset-tagify/-/preset-tagify-0.65.4.tgz"
  integrity sha512-qll6koqdFEkvmz594vKnxj9+3nfM3ugkJxYHrTkqtwx7DAnTgtM8fInFFGZelvjwUzR3o3+Zw6uMhFkLTVTfvg==
  dependencies:
    "@unocss/core" "0.65.4"

"@unocss/preset-typography@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/preset-typography/-/preset-typography-0.65.4.tgz"
  integrity sha512-Dl940ATrviWD9Vh+4fcN0QZXb6wA7al+c7QkdVAzW7I+NtdN2ELvLcN0cY22KnLRpwztzmg52Qp2J/1QnqrLTw==
  dependencies:
    "@unocss/core" "0.65.4"
    "@unocss/preset-mini" "0.65.4"

"@unocss/preset-uno@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/preset-uno/-/preset-uno-0.65.4.tgz"
  integrity sha512-56bdBtf476i+soQCQmT36uGzcF2z+7DGCnG1hwWiw6XAbL6gmRMQsubwi1c8z8TcTQNBsOFUnOziFil0gbWufw==
  dependencies:
    "@unocss/core" "0.65.4"
    "@unocss/preset-mini" "0.65.4"
    "@unocss/preset-wind" "0.65.4"
    "@unocss/rule-utils" "0.65.4"

"@unocss/preset-web-fonts@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/preset-web-fonts/-/preset-web-fonts-0.65.4.tgz"
  integrity sha512-UB/MvXHUTqMNVH1bbiKZ/ZtZUI5tsYlTYAvBrnXPO1Cztuwr8hJKSi4RCfI9g+YYtKHX4uYuxUbW5bcN85gmBQ==
  dependencies:
    "@unocss/core" "0.65.4"
    ofetch "^1.4.1"

"@unocss/preset-wind@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/preset-wind/-/preset-wind-0.65.4.tgz"
  integrity sha512-0rbNbw5E8Lvh2yf4R1Mq+lxI/wL5Tm6+r+crE0uAAhCPe9kxPHW4k+x1cWKDIwq6Vudlm3cNX85N49wN5tYgdA==
  dependencies:
    "@unocss/core" "0.65.4"
    "@unocss/preset-mini" "0.65.4"
    "@unocss/rule-utils" "0.65.4"

"@unocss/reset@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/reset/-/reset-0.65.4.tgz"
  integrity sha512-m685H0KFvVMz6R2i5GDIFv4RS9Z7y2G8hJK7xg2OWli+7w8l2ZMihYvXKofPsst4q/ms8EgKXpWc/qqUOTucvA==

"@unocss/rule-utils@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/rule-utils/-/rule-utils-0.65.4.tgz"
  integrity sha512-+EzdJEWcqGcO6HwbBTe7vEdBRpuKkBiz4MycQeLD6GEio04T45y6VHHO7/WTqxltbO4YwwW9/s2TKRMxKtoG8g==
  dependencies:
    "@unocss/core" "^0.65.4"
    magic-string "^0.30.17"

"@unocss/transformer-attributify-jsx@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/transformer-attributify-jsx/-/transformer-attributify-jsx-0.65.4.tgz"
  integrity sha512-n438EzWdTKlLCOlAUSpFjmH6FflctqzIReMzMZSJDkmkorymc+C5GpjN3Nty2cKRJXIl6Vwq0oxPuB59RT+FIw==
  dependencies:
    "@unocss/core" "0.65.4"

"@unocss/transformer-compile-class@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/transformer-compile-class/-/transformer-compile-class-0.65.4.tgz"
  integrity sha512-n1yHDC/iIbcj/9fBUTXkSoASKfLBuRoCN7P1a0ecPc8Gu+uOGfoxafOhrlqC+tpD3hlQGoL+0h74BHSKh+L23Q==
  dependencies:
    "@unocss/core" "0.65.4"

"@unocss/transformer-directives@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/transformer-directives/-/transformer-directives-0.65.4.tgz"
  integrity sha512-zkoDEwzPkgXi6ohW7P11gbArwfTRMZ9knYSUYoPEltQz+UZYzeRQ85exiAmdz5MsbCAuhQEr577Kd/CWfhjEuA==
  dependencies:
    "@unocss/core" "0.65.4"
    "@unocss/rule-utils" "0.65.4"
    css-tree "^3.1.0"

"@unocss/transformer-variant-group@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/transformer-variant-group/-/transformer-variant-group-0.65.4.tgz"
  integrity sha512-ggO6xMGeOeoD5GHS2xXBJrYFuzqyiZ25tM0zHAMJn9QU9GIu1NwWvcXluvLCF/MRIygBJGPpAE98aEICI6ifEA==
  dependencies:
    "@unocss/core" "0.65.4"

"@unocss/vite@0.65.4":
  version "0.65.4"
  resolved "https://registry.npmjs.org/@unocss/vite/-/vite-0.65.4.tgz"
  integrity sha512-02pRcVLfb5UUxMJwudnjS/0ZQdSlskjuXVHdpZpLBZCA8hhoru2uEOsPbUOBRNNMjDj6ld00pmgk/+im07M35Q==
  dependencies:
    "@ampproject/remapping" "^2.3.0"
    "@rollup/pluginutils" "^5.1.4"
    "@unocss/config" "0.65.4"
    "@unocss/core" "0.65.4"
    "@unocss/inspector" "0.65.4"
    chokidar "^3.6.0"
    magic-string "^0.30.17"
    tinyglobby "^0.2.10"

"@vitejs/plugin-vue@^5.1.4":
  version "5.2.4"
  resolved "https://registry.npmjs.org/@vitejs/plugin-vue/-/plugin-vue-5.2.4.tgz"
  integrity sha512-7Yx/SXSOcQq5HiiV3orevHUFn+pmMB4cgbEkDYgnkUWb0WfeQ/wa2yFv6D5ICiCQOVpjA7vYDXrC7AGO8yjDHA==

"@vue/compiler-core@3.5.17":
  version "3.5.17"
  resolved "https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.5.17.tgz"
  integrity sha512-Xe+AittLbAyV0pabcN7cP7/BenRBNcteM4aSDCtRvGw0d9OL+HG1u/XHLY/kt1q4fyMeZYXyIYrsHuPSiDPosA==
  dependencies:
    "@babel/parser" "^7.27.5"
    "@vue/shared" "3.5.17"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.1"

"@vue/compiler-dom@3.5.17":
  version "3.5.17"
  resolved "https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.5.17.tgz"
  integrity sha512-+2UgfLKoaNLhgfhV5Ihnk6wB4ljyW1/7wUIog2puUqajiC29Lp5R/IKDdkebh9jTbTogTbsgB+OY9cEWzG95JQ==
  dependencies:
    "@vue/compiler-core" "3.5.17"
    "@vue/shared" "3.5.17"

"@vue/compiler-sfc@3.5.17":
  version "3.5.17"
  resolved "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.5.17.tgz"
  integrity sha512-rQQxbRJMgTqwRugtjw0cnyQv9cP4/4BxWfTdRBkqsTfLOHWykLzbOc3C4GGzAmdMDxhzU/1Ija5bTjMVrddqww==
  dependencies:
    "@babel/parser" "^7.27.5"
    "@vue/compiler-core" "3.5.17"
    "@vue/compiler-dom" "3.5.17"
    "@vue/compiler-ssr" "3.5.17"
    "@vue/shared" "3.5.17"
    estree-walker "^2.0.2"
    magic-string "^0.30.17"
    postcss "^8.5.6"
    source-map-js "^1.2.1"

"@vue/compiler-ssr@3.5.17":
  version "3.5.17"
  resolved "https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.5.17.tgz"
  integrity sha512-hkDbA0Q20ZzGgpj5uZjb9rBzQtIHLS78mMilwrlpWk2Ep37DYntUz0PonQ6kr113vfOEdM+zTBuJDaceNIW0tQ==
  dependencies:
    "@vue/compiler-dom" "3.5.17"
    "@vue/shared" "3.5.17"

"@vue/devtools-api@^6.5.0", "@vue/devtools-api@^6.6.4":
  version "6.6.4"
  resolved "https://registry.npmjs.org/@vue/devtools-api/-/devtools-api-6.6.4.tgz"
  integrity sha512-sGhTPMuXqZ1rVOk32RylztWkfXTRhuS7vgAKv0zjqk8gbsHkJ7xfFf+jbySxt7tWObEJwyKaHMikV/WGDiQm8g==

"@vue/reactivity@3.5.17":
  version "3.5.17"
  resolved "https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.5.17.tgz"
  integrity sha512-l/rmw2STIscWi7SNJp708FK4Kofs97zc/5aEPQh4bOsReD/8ICuBcEmS7KGwDj5ODQLYWVN2lNibKJL1z5b+Lw==
  dependencies:
    "@vue/shared" "3.5.17"

"@vue/runtime-core@3.5.17":
  version "3.5.17"
  resolved "https://registry.npmjs.org/@vue/runtime-core/-/runtime-core-3.5.17.tgz"
  integrity sha512-QQLXa20dHg1R0ri4bjKeGFKEkJA7MMBxrKo2G+gJikmumRS7PTD4BOU9FKrDQWMKowz7frJJGqBffYMgQYS96Q==
  dependencies:
    "@vue/reactivity" "3.5.17"
    "@vue/shared" "3.5.17"

"@vue/runtime-dom@3.5.17":
  version "3.5.17"
  resolved "https://registry.npmjs.org/@vue/runtime-dom/-/runtime-dom-3.5.17.tgz"
  integrity sha512-8El0M60TcwZ1QMz4/os2MdlQECgGoVHPuLnQBU3m9h3gdNRW9xRmI8iLS4t/22OQlOE6aJvNNlBiCzPHur4H9g==
  dependencies:
    "@vue/reactivity" "3.5.17"
    "@vue/runtime-core" "3.5.17"
    "@vue/shared" "3.5.17"
    csstype "^3.1.3"

"@vue/server-renderer@3.5.17":
  version "3.5.17"
  resolved "https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.5.17.tgz"
  integrity sha512-BOHhm8HalujY6lmC3DbqF6uXN/K00uWiEeF22LfEsm9Q93XeJ/plHTepGwf6tqFcF7GA5oGSSAAUock3VvzaCA==
  dependencies:
    "@vue/compiler-ssr" "3.5.17"
    "@vue/shared" "3.5.17"

"@vue/shared@3.5.17":
  version "3.5.17"
  resolved "https://registry.npmjs.org/@vue/shared/-/shared-3.5.17.tgz"
  integrity sha512-CabR+UN630VnsJO/jHWYBC1YVXyMq94KKp6iF5MQgZJs5I8cmjw6oVMO1oDbtBkENSHSSn/UadWlW/OAgdmKrg==

"@vueuse/core@^11.1.0", "@vueuse/core@>=10":
  version "11.3.0"
  resolved "https://registry.npmjs.org/@vueuse/core/-/core-11.3.0.tgz"
  integrity sha512-7OC4Rl1f9G8IT6rUfi9JrKiXy4bfmHhZ5x2Ceojy0jnd3mHNEvV4JaRygH362ror6/NZ+Nl+n13LPzGiPN8cKA==
  dependencies:
    "@types/web-bluetooth" "^0.0.20"
    "@vueuse/metadata" "11.3.0"
    "@vueuse/shared" "11.3.0"
    vue-demi ">=0.14.10"

"@vueuse/core@^9.1.0":
  version "9.13.0"
  resolved "https://registry.npmjs.org/@vueuse/core/-/core-9.13.0.tgz"
  integrity sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw==
  dependencies:
    "@types/web-bluetooth" "^0.0.16"
    "@vueuse/metadata" "9.13.0"
    "@vueuse/shared" "9.13.0"
    vue-demi "*"

"@vueuse/metadata@11.3.0":
  version "11.3.0"
  resolved "https://registry.npmjs.org/@vueuse/metadata/-/metadata-11.3.0.tgz"
  integrity sha512-pwDnDspTqtTo2HwfLw4Rp6yywuuBdYnPYDq+mO38ZYKGebCUQC/nVj/PXSiK9HX5otxLz8Fn7ECPbjiRz2CC3g==

"@vueuse/metadata@9.13.0":
  version "9.13.0"
  resolved "https://registry.npmjs.org/@vueuse/metadata/-/metadata-9.13.0.tgz"
  integrity sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ==

"@vueuse/shared@11.3.0":
  version "11.3.0"
  resolved "https://registry.npmjs.org/@vueuse/shared/-/shared-11.3.0.tgz"
  integrity sha512-P8gSSWQeucH5821ek2mn/ciCk+MS/zoRKqdQIM3bHq6p7GXDAJLmnRRKmF5F65sAVJIfzQlwR3aDzwCn10s8hA==
  dependencies:
    vue-demi ">=0.14.10"

"@vueuse/shared@9.13.0":
  version "9.13.0"
  resolved "https://registry.npmjs.org/@vueuse/shared/-/shared-9.13.0.tgz"
  integrity sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw==
  dependencies:
    vue-demi "*"

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz"
  integrity sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==
  dependencies:
    event-target-shim "^5.0.0"

accepts@^1.3.5:
  version "1.3.8"
  resolved "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz"
  integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn@^8.14.0:
  version "8.15.0"
  resolved "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz"
  integrity sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==

agent-base@^7.1.0, agent-base@^7.1.2:
  version "7.1.3"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz"
  integrity sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/aggregate-error/-/aggregate-error-3.1.0.tgz"
  integrity sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

anymatch@^3.1.3, anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz"
  integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==

asap@^2.0.0:
  version "2.0.6"
  resolved "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz"
  integrity sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==

ast-types@^0.13.4:
  version "0.13.4"
  resolved "https://registry.npmjs.org/ast-types/-/ast-types-0.13.4.tgz"
  integrity sha512-x1FCFnFifvYDDzTaLII71vG5uvDwgtmDTEVWAxrgeiR8VjMONcCXJx7E+USjDtHlwFmt9MysbqgF9b9Vjr6w+w==
  dependencies:
    tslib "^2.0.1"

async-validator@^4.2.5:
  version "4.2.5"
  resolved "https://registry.npmjs.org/async-validator/-/async-validator-4.2.5.tgz"
  integrity sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==

atsc@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/atsc/-/atsc-2.1.0.tgz"
  integrity sha512-0B2DzsN+PRsRvUZV+e1NQtUV08DoBxdTvXrYt1MP+0VcqsfIVz0lIT0GKMr9u5FXj81OVee1RTwXOpsUvmieEQ==
  dependencies:
    globby "^11.1.0"
    js-yaml "^4.1.0"
    tsconfig-utils "^4.0.5"

b4a@^1.6.4:
  version "1.6.7"
  resolved "https://registry.npmjs.org/b4a/-/b4a-1.6.7.tgz"
  integrity sha512-OnAYlL5b7LEkALw87fUVafQw5rVR9RjwGd4KUwNQ6DrrNmaVaUCgLipfVlzrPQ4tWOR9P0IXGNOx50jYCCdSJg==

bare-events@*, bare-events@^2.2.0, bare-events@^2.5.4:
  version "2.5.4"
  resolved "https://registry.npmjs.org/bare-events/-/bare-events-2.5.4.tgz"
  integrity sha512-+gFfDkR8pj4/TrWCGUGWmJIkBwuxPS5F+a5yWjOHQt2hHvNZd5YLzadjmDUtFmMM4y429bnKLa8bYBMHcYdnQA==

bare-fs@^4.0.1:
  version "4.1.5"
  resolved "https://registry.npmjs.org/bare-fs/-/bare-fs-4.1.5.tgz"
  integrity sha512-1zccWBMypln0jEE05LzZt+V/8y8AQsQQqxtklqaIyg5nu6OAYFhZxPXinJTSG+kU5qyNmeLgcn9AW7eHiCHVLA==
  dependencies:
    bare-events "^2.5.4"
    bare-path "^3.0.0"
    bare-stream "^2.6.4"

bare-os@^3.0.1:
  version "3.6.1"
  resolved "https://registry.npmjs.org/bare-os/-/bare-os-3.6.1.tgz"
  integrity sha512-uaIjxokhFidJP+bmmvKSgiMzj2sV5GPHaZVAIktcxcpCyBFFWO+YlikVAdhmUo2vYFvFhOXIAlldqV29L8126g==

bare-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/bare-path/-/bare-path-3.0.0.tgz"
  integrity sha512-tyfW2cQcB5NN8Saijrhqn0Zh7AnFNsnczRcuWODH0eYAXBsJ5gVxAUuNr7tsHSC6IZ77cA0SitzT+s47kot8Mw==
  dependencies:
    bare-os "^3.0.1"

bare-stream@^2.6.4:
  version "2.6.5"
  resolved "https://registry.npmjs.org/bare-stream/-/bare-stream-2.6.5.tgz"
  integrity sha512-jSmxKJNJmHySi6hC42zlZnq00rga4jjxcgNZjY9N5WlOe/iOoGRtdwGsHzQv2RlH2KOYMwGUXhf2zXd32BA9RA==
  dependencies:
    streamx "^2.21.0"

base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

basic-ftp@^5.0.2:
  version "5.0.5"
  resolved "https://registry.npmjs.org/basic-ftp/-/basic-ftp-5.0.5.tgz"
  integrity sha512-4Bcg1P8xhUuqcii/S0Z9wiHIrQVPMermM1any+MX5GeGD7faD3/msQUDGLol9wOcz4/jbg/WJnGqoJF6LiBdtg==

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz"
  integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==

bl@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz"
  integrity sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
  integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
  dependencies:
    fill-range "^7.1.1"

buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz"
  integrity sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==

buffer@^5.2.1:
  version "5.7.1"
  resolved "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

buffer@^5.5.0:
  version "5.7.1"
  resolved "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

buffer@^6.0.3:
  version "6.0.3"
  resolved "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz"
  integrity sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

bundle-require@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/bundle-require/-/bundle-require-5.1.0.tgz"
  integrity sha512-3WrrOuZiyaaZPWiEt4G3+IffISVC9HYlWueJEBWED4ZH4aIAC2PnkdnuRrR94M+w6yGWn4AglWtJtBI8YqvgoA==
  dependencies:
    load-tsconfig "^0.2.3"

bytes@3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz"
  integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==

cac@^6.7.14:
  version "6.7.14"
  resolved "https://registry.npmjs.org/cac/-/cac-6.7.14.tgz"
  integrity sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==

cache-content-type@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/cache-content-type/-/cache-content-type-1.0.1.tgz"
  integrity sha512-IKufZ1o4Ut42YUrZSo8+qnMTrFuKkvyoLXUywKz9GJ5BrhOFGhLdkx9sG4KAnVvbY6kEcSFjLQul+DVmBm2bgA==
  dependencies:
    mime-types "^2.1.18"
    ylru "^1.2.0"

call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz"
  integrity sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bound@^1.0.2, call-bound@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz"
  integrity sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

camelcase@^6.0.0:
  version "6.3.0"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz"
  integrity sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==

chalk@^4.1.0:
  version "4.1.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/chardet/-/chardet-2.1.0.tgz"
  integrity sha512-bNFETTG/pM5ryzQ9Ad0lJOTa6HWD/YsScAR3EnCPZRPlQh77JocYktSHOUHelyhm8IARL+o4c4F1bP5KVOjiRA==

chokidar@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chokidar@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-4.0.3.tgz"
  integrity sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==
  dependencies:
    readdirp "^4.0.1"

chromium-bidi@0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/chromium-bidi/-/chromium-bidi-0.6.3.tgz"
  integrity sha512-qXlsCmpCZJAnoTYI83Iu6EdYQpMYdVkCfq08KDh2pmlVqK5t5IA9mGs4/LwCwp4fqisSOMXZxP3HIh8w8aRn0A==
  dependencies:
    mitt "3.0.1"
    urlpattern-polyfill "10.0.0"
    zod "3.23.8"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/clean-stack/-/clean-stack-2.2.0.tgz"
  integrity sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz"
  integrity sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==
  dependencies:
    restore-cursor "^3.1.0"

cli-spinners@^2.5.0:
  version "2.9.2"
  resolved "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz"
  integrity sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz"
  integrity sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz"
  integrity sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==

co-body@^6.1.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/co-body/-/co-body-6.2.0.tgz"
  integrity sha512-Kbpv2Yd1NdL1V/V4cwLVxraHDV6K8ayohr2rmH0J87Er8+zJjcTa6dAn9QMPC9CRgU8+aNajKbSf1TzDB1yKPA==
  dependencies:
    "@hapi/bourne" "^3.0.0"
    inflation "^2.0.0"
    qs "^6.5.2"
    raw-body "^2.3.3"
    type-is "^1.6.16"

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/co/-/co-4.6.0.tgz"
  integrity sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

colorette@^2.0.20:
  version "2.0.20"
  resolved "https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz"
  integrity sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==

commander@^2.20.3:
  version "2.20.3"
  resolved "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==

confbox@^0.1.8:
  version "0.1.8"
  resolved "https://registry.npmjs.org/confbox/-/confbox-0.1.8.tgz"
  integrity sha512-RMtmw0iFkeR4YV+fUOSucriAQNb9g8zFR52MWCtl+cCZOFRNL6zeB395vPzFhEjjn4fMxXudmELnl/KF/WrK6w==

confbox@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmjs.org/confbox/-/confbox-0.2.2.tgz"
  integrity sha512-1NB+BKqhtNipMsov4xI/NnhCKp9XG9NamYp5PVm9klAT0fsrNPjaFICsCFhNhwZJKNh7zB/3q8qXz0E9oaMNtQ==

consola@^3.3.1:
  version "3.4.2"
  resolved "https://registry.npmjs.org/consola/-/consola-3.4.2.tgz"
  integrity sha512-5IKcdX0nnYavi6G7TtOhwkYzyjfJlatbjMjuLSfE2kYT5pMDOilZ4OvMhi637CcDICTmz3wARPoyhqyX1Y+XvA==

content-disposition@~0.5.2:
  version "0.5.4"
  resolved "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz"
  integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
  dependencies:
    safe-buffer "5.2.1"

content-type@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz"
  integrity sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==

cookies@~0.9.0:
  version "0.9.1"
  resolved "https://registry.npmjs.org/cookies/-/cookies-0.9.1.tgz"
  integrity sha512-TG2hpqe4ELx54QER/S3HQ9SRVnQnGBtKUz5bLQWtYAQ+o6GpgMs6sYUvaiJjVxb+UXwhRhAEP3m7LbsIZ77Hmw==
  dependencies:
    depd "~2.0.0"
    keygrip "~1.1.0"

cordis@*, cordis@^3.17.4, cordis@^3.18.1:
  version "3.18.1"
  resolved "https://registry.npmjs.org/cordis/-/cordis-3.18.1.tgz"
  integrity sha512-9IbthFbFBVJ15WBDNPqHdl59TyEAMNWxF1Dxsdi14625ePQkMX35WvcqgGno0BcXlpXDbUaBZgQ+jfBYT+uuSQ==
  dependencies:
    "@cordisjs/core" "3.18.1"
    "@cordisjs/loader" "^0.13.1"
    "@cordisjs/logger" "^0.3.3"
    "@cordisjs/schema" "^0.1.1"
    "@cordisjs/timer" "^0.3.2"
    cac "^6.7.14"
    cosmokit "^1.6.2"
    kleur "^4.1.5"
    reggol "^1.7.0"

cosmokit@^1.5.2, cosmokit@^1.6.2, cosmokit@^1.6.3, cosmokit@^1.7.2:
  version "1.8.0"
  resolved "https://registry.npmjs.org/cosmokit/-/cosmokit-1.8.0.tgz"
  integrity sha512-iTnjvOdRKzmcZGSTamFW/pwlDLNBKkIEjk43Ew40YaxSayogY6OsCV40sSTb2dlvD3stR14bLh04qOoQZzX/0Q==

cross-env@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmjs.org/cross-env/-/cross-env-7.0.3.tgz"
  integrity sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==
  dependencies:
    cross-spawn "^7.0.1"

cross-spawn@^7.0.1, cross-spawn@^7.0.3:
  version "7.0.6"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz"
  integrity sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-tree@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/css-tree/-/css-tree-3.1.0.tgz"
  integrity sha512-0eW44TGN5SQXU1mWSkKwFstI/22X2bG1nYzZTYMAWjylYURhse752YgbE4Cx46AC+bAvI+/dYTPRk1LqSUnu6w==
  dependencies:
    mdn-data "2.12.2"
    source-map-js "^1.0.1"

cssfilter@0.0.10:
  version "0.0.10"
  resolved "https://registry.npmjs.org/cssfilter/-/cssfilter-0.0.10.tgz"
  integrity sha512-FAaLDaplstoRsDR8XGYH51znUN0UY7nMc6Z9/fvE8EXGwvJE9hu7W2vHwx1+bd6gCYnln9nLbzxFTrcO9YQDZw==

csstype@^3.0.2, csstype@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

data-uri-to-buffer@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-6.0.2.tgz"
  integrity sha512-7hvf7/GW8e86rW0ptuwS3OcBGDjIi6SZva7hCyWC0yYry2cOPmLIjXAUHI6DK2HsnwJd9ifmt57i8eV2n4YNpw==

dayjs@^1.11.3:
  version "1.11.13"
  resolved "https://registry.npmjs.org/dayjs/-/dayjs-1.11.13.tgz"
  integrity sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==

debug@^4.1.1, debug@^4.3.2, debug@^4.3.4, debug@^4.3.5, debug@^4.3.6, debug@^4.4.0, debug@4:
  version "4.4.1"
  resolved "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz"
  integrity sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==
  dependencies:
    ms "^2.1.3"

decamelize@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/decamelize/-/decamelize-4.0.0.tgz"
  integrity sha512-9iE1PgSik9HeIIw2JO94IidnE3eBoQrFJ3w7sFuzSX4DpmZ3v5sZpUiV5Swcf6mQEF+Y0ru8Neo+p+nyh2J+hQ==

deep-equal@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/deep-equal/-/deep-equal-1.0.1.tgz"
  integrity sha512-bHtC0iYvWhyaTzvV3CZgPeZQqCOBGyGsVV7v4eevpdkLHfiSrXUdBG+qAuSz4RI70sszvjQ1QSZ98An1yNwpSw==

defaults@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz"
  integrity sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==
  dependencies:
    clone "^1.0.2"

define-lazy-prop@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz"
  integrity sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==

defu@^6.1.4:
  version "6.1.4"
  resolved "https://registry.npmjs.org/defu/-/defu-6.1.4.tgz"
  integrity sha512-mEQCMmwJu317oSz8CwdIOdwf3xMif1ttiM8LTufzc3g6kR+9Pe236twL8j3IYT1F7GfRgGcW6MWxzZjLIkuHIg==

degenerator@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/degenerator/-/degenerator-5.0.1.tgz"
  integrity sha512-TllpMR/t0M5sqCXfj85i4XaAzxmS5tVA16dqvdkMwGmzI+dXLXnw3J+3Vdv7VKw+ThlTMboK6i9rnZ6Nntj5CQ==
  dependencies:
    ast-types "^0.13.4"
    escodegen "^2.1.0"
    esprima "^4.0.1"

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz"
  integrity sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==

depd@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz"
  integrity sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==

depd@~2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

depd@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

destr@^2.0.3:
  version "2.0.5"
  resolved "https://registry.npmjs.org/destr/-/destr-2.0.5.tgz"
  integrity sha512-ugFTXCtDZunbzasqBxrK93Ik/DRYsO6S/fedkWEMKqt04xZ4csmnmwGDBAb07QWNaGMAmnTIemsYZCksjATwsA==

destroy@^1.0.4:
  version "1.2.0"
  resolved "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz"
  integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==

detect-indent@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmjs.org/detect-indent/-/detect-indent-6.1.0.tgz"
  integrity sha512-reYkTUJAZb9gUuZ2RvVCNhVHdg62RHnJ7WJl8ftMi4diZ6NWlciOzQN88pUhSELEwflJht4oQDv0F0BMlwaYtA==

detect-libc@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz"
  integrity sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==

devtools-protocol@*, devtools-protocol@0.0.1312386:
  version "0.0.1312386"
  resolved "https://registry.npmjs.org/devtools-protocol/-/devtools-protocol-0.0.1312386.tgz"
  integrity sha512-DPnhUXvmvKT2dFA/j7B+riVLUt9Q6RKJlcppojL5CoRywJJKLDYnRlw0gTFKfgDPHP5E04UoB71SxoJlVZy8FA==

dezalgo@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/dezalgo/-/dezalgo-1.0.4.tgz"
  integrity sha512-rXSP0bf+5n0Qonsb+SVVfNfIsimO4HEtmnIpPHY8Q1UCzKlQrDMfdobr8nJOOsRgWCyMRqeSBQzmWUMq7zvVig==
  dependencies:
    asap "^2.0.0"
    wrappy "1"

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
  dependencies:
    path-type "^4.0.0"

dotenv@^16.4.5:
  version "16.6.1"
  resolved "https://registry.npmjs.org/dotenv/-/dotenv-16.6.1.tgz"
  integrity sha512-uBq4egWHTcTt33a72vpSG0z3HnPuIl6NqYcTrKEg2azoEyl2hpW0zqlxysq2pK9HlDIHyHyakeYaYnSAwd8bow==

dtsc@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/dtsc/-/dtsc-3.1.0.tgz"
  integrity sha512-d6nAl8CHKFYOywx25lpLrkh5hnL+IYMf2ucRVy3WtwltF78/l06GTPmoj1z3/GRCJ7by49oNCHDrx82wyNDqmw==
  dependencies:
    tsconfig-utils "^4.1.1"

dumble@^0.2.1:
  version "0.2.2"
  resolved "https://registry.npmjs.org/dumble/-/dumble-0.2.2.tgz"
  integrity sha512-46ig2gkHW2mJnAUvebaamW6kGV+MapIlKUuzS8LEsUDHfx+3eW0rHwT6rSjMn3ZIVeb4+o13VAtCOrCgJmNGDw==
  dependencies:
    cac "^6.7.14"
    globby "^11.1.0"
    js-yaml "^4.1.0"
    kleur "^4.1.5"
    tsconfig-utils "^4.0.5"

dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz"
  integrity sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

duplexer@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/duplexer/-/duplexer-0.1.2.tgz"
  integrity sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==

element-plus@^2, element-plus@2.7.7:
  version "2.7.7"
  resolved "https://registry.npmjs.org/element-plus/-/element-plus-2.7.7.tgz"
  integrity sha512-7ucUiDAxevyBE8JbXBTe9ofHhS047VmWMLoksE45zZ08XSnhnyG7WUuk3gmDbAklfVMHedb9sEV3OovPUWt+Sw==
  dependencies:
    "@ctrl/tinycolor" "^3.4.1"
    "@element-plus/icons-vue" "^2.3.1"
    "@floating-ui/dom" "^1.0.1"
    "@popperjs/core" "npm:@sxzz/popperjs-es@^2.11.7"
    "@types/lodash" "^4.14.182"
    "@types/lodash-es" "^4.17.6"
    "@vueuse/core" "^9.1.0"
    async-validator "^4.2.5"
    dayjs "^1.11.3"
    escape-html "^1.0.3"
    lodash "^4.17.21"
    lodash-es "^4.17.21"
    lodash-unified "^1.0.2"
    memoize-one "^6.0.0"
    normalize-wheel-es "^1.2.0"

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

encodeurl@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  integrity sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==

end-of-stream@^1.1.0:
  version "1.4.5"
  resolved "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.5.tgz"
  integrity sha512-ooEGc6HP26xXq/N+GCGOT0JKCLDGrq2bQUZrQ7gyrJiZANJ/8YDTxTpQBXGMn+WbIQXNVpyWymm7KYVICQnyOg==
  dependencies:
    once "^1.4.0"

entities@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz"
  integrity sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==

envinfo@^7.11.0:
  version "7.14.0"
  resolved "https://registry.npmjs.org/envinfo/-/envinfo-7.14.0.tgz"
  integrity sha512-CO40UI41xDQzhLB1hWyqUKgFhs250pNcGbyGKe1l/e4FSaI/+YE4IMG76GDt0In67WLPACIITC+sOi08x4wIvg==

es-define-property@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz"
  integrity sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz"
  integrity sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==
  dependencies:
    es-errors "^1.3.0"

"esbuild-register@npm:@shigma/esbuild-register@^1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@shigma/esbuild-register/-/esbuild-register-1.1.1.tgz"
  integrity sha512-BHnWxTaoaIKip0m5//A0h2k7UTAveDkSnvXSSrnYWCE9OljgLwkm6rjYo/WXgUrCSgZisSYcTraDS4t7V1bfzA==
  dependencies:
    debug "^4.3.4"

esbuild@*, "esbuild@^0.20.2 || ^0.21.0 || ^0.22.0 || ^0.23.0 || ^0.24.0 || ^0.25.0", esbuild@^0.23.1, "esbuild@>=0.12 <1", esbuild@>=0.18:
  version "0.23.1"
  resolved "https://registry.npmjs.org/esbuild/-/esbuild-0.23.1.tgz"
  integrity sha512-VVNz/9Sa0bs5SELtn3f7qhJCDPCF5oMEl5cO9/SSinpE9hbPVvxbd572HH5AKiP7WD8INO53GgfDDhRjkylHEg==
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.23.1"
    "@esbuild/android-arm" "0.23.1"
    "@esbuild/android-arm64" "0.23.1"
    "@esbuild/android-x64" "0.23.1"
    "@esbuild/darwin-arm64" "0.23.1"
    "@esbuild/darwin-x64" "0.23.1"
    "@esbuild/freebsd-arm64" "0.23.1"
    "@esbuild/freebsd-x64" "0.23.1"
    "@esbuild/linux-arm" "0.23.1"
    "@esbuild/linux-arm64" "0.23.1"
    "@esbuild/linux-ia32" "0.23.1"
    "@esbuild/linux-loong64" "0.23.1"
    "@esbuild/linux-mips64el" "0.23.1"
    "@esbuild/linux-ppc64" "0.23.1"
    "@esbuild/linux-riscv64" "0.23.1"
    "@esbuild/linux-s390x" "0.23.1"
    "@esbuild/linux-x64" "0.23.1"
    "@esbuild/netbsd-x64" "0.23.1"
    "@esbuild/openbsd-arm64" "0.23.1"
    "@esbuild/openbsd-x64" "0.23.1"
    "@esbuild/sunos-x64" "0.23.1"
    "@esbuild/win32-arm64" "0.23.1"
    "@esbuild/win32-ia32" "0.23.1"
    "@esbuild/win32-x64" "0.23.1"

esbuild@^0.21.3:
  version "0.21.5"
  resolved "https://registry.npmjs.org/esbuild/-/esbuild-0.21.5.tgz"
  integrity sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.21.5"
    "@esbuild/android-arm" "0.21.5"
    "@esbuild/android-arm64" "0.21.5"
    "@esbuild/android-x64" "0.21.5"
    "@esbuild/darwin-arm64" "0.21.5"
    "@esbuild/darwin-x64" "0.21.5"
    "@esbuild/freebsd-arm64" "0.21.5"
    "@esbuild/freebsd-x64" "0.21.5"
    "@esbuild/linux-arm" "0.21.5"
    "@esbuild/linux-arm64" "0.21.5"
    "@esbuild/linux-ia32" "0.21.5"
    "@esbuild/linux-loong64" "0.21.5"
    "@esbuild/linux-mips64el" "0.21.5"
    "@esbuild/linux-ppc64" "0.21.5"
    "@esbuild/linux-riscv64" "0.21.5"
    "@esbuild/linux-s390x" "0.21.5"
    "@esbuild/linux-x64" "0.21.5"
    "@esbuild/netbsd-x64" "0.21.5"
    "@esbuild/openbsd-x64" "0.21.5"
    "@esbuild/sunos-x64" "0.21.5"
    "@esbuild/win32-arm64" "0.21.5"
    "@esbuild/win32-ia32" "0.21.5"
    "@esbuild/win32-x64" "0.21.5"

esbuild@~0.25.0:
  version "0.25.5"
  resolved "https://registry.npmjs.org/esbuild/-/esbuild-0.25.5.tgz"
  integrity sha512-P8OtKZRv/5J5hhz0cUAdu/cLuPIKXpQl1R9pZtvmHWQvrAUVd0UNIPT4IB4W3rNOqVO0rlqHmCIbSwxh/c9yUQ==
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.25.5"
    "@esbuild/android-arm" "0.25.5"
    "@esbuild/android-arm64" "0.25.5"
    "@esbuild/android-x64" "0.25.5"
    "@esbuild/darwin-arm64" "0.25.5"
    "@esbuild/darwin-x64" "0.25.5"
    "@esbuild/freebsd-arm64" "0.25.5"
    "@esbuild/freebsd-x64" "0.25.5"
    "@esbuild/linux-arm" "0.25.5"
    "@esbuild/linux-arm64" "0.25.5"
    "@esbuild/linux-ia32" "0.25.5"
    "@esbuild/linux-loong64" "0.25.5"
    "@esbuild/linux-mips64el" "0.25.5"
    "@esbuild/linux-ppc64" "0.25.5"
    "@esbuild/linux-riscv64" "0.25.5"
    "@esbuild/linux-s390x" "0.25.5"
    "@esbuild/linux-x64" "0.25.5"
    "@esbuild/netbsd-arm64" "0.25.5"
    "@esbuild/netbsd-x64" "0.25.5"
    "@esbuild/openbsd-arm64" "0.25.5"
    "@esbuild/openbsd-x64" "0.25.5"
    "@esbuild/sunos-x64" "0.25.5"
    "@esbuild/win32-arm64" "0.25.5"
    "@esbuild/win32-ia32" "0.25.5"
    "@esbuild/win32-x64" "0.25.5"

escalade@^3.1.1:
  version "3.2.0"
  resolved "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  integrity sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==

escape-html@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==

escodegen@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/escodegen/-/escodegen-2.1.0.tgz"
  integrity sha512-2NlIDTwUWJN0mRPQOdtQBzbUHvdGY2P1VXSyU83Q3xKxM7WHX2Ql8dKq782Q9TgQUNOLEzEYu9bzLNj1q88I5w==
  dependencies:
    esprima "^4.0.1"
    estraverse "^5.2.0"
    esutils "^2.0.2"
  optionalDependencies:
    source-map "~0.6.1"

esprima@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz"
  integrity sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz"
  integrity sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==

events@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

execa@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz"
  integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exsolve@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/exsolve/-/exsolve-1.0.7.tgz"
  integrity sha512-VO5fQUzZtI6C+vx4w/4BWJpg3s/5l+6pRQEHzFRM8WFi4XffSP1Z+4qi7GbjWbvRQEbdIco5mIMq+zX4rPuLrw==

extract-zip@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/extract-zip/-/extract-zip-2.0.1.tgz"
  integrity sha512-GDhU9ntwuKyGXdZBUgTIe+vXnWj0fppUEtMDL0+idd5Sta8TGpHssn/eusA9mrPr9qNDym6SxAYZjNvCn/9RBg==
  dependencies:
    debug "^4.1.1"
    get-stream "^5.1.0"
    yauzl "^2.10.0"
  optionalDependencies:
    "@types/yauzl" "^2.9.1"

fast-fifo@^1.2.0, fast-fifo@^1.3.2:
  version "1.3.2"
  resolved "https://registry.npmjs.org/fast-fifo/-/fast-fifo-1.3.2.tgz"
  integrity sha512-/d9sfos4yxzpwkDkuN7k2SqFKtYNmCTzgfEpz82x34IM9/zc8KGxQoXg1liNC/izpRM/MBdt44Nmx41ZWqk+FQ==

fast-glob@^3.2.9:
  version "3.3.3"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz"
  integrity sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fastest-levenshtein@^1.0.16:
  version "1.0.16"
  resolved "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz"
  integrity sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==

fastq@^1.6.0:
  version "1.19.1"
  resolved "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz"
  integrity sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==
  dependencies:
    reusify "^1.0.4"

fd-slicer@~1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.1.0.tgz"
  integrity sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g==
  dependencies:
    pend "~1.2.0"

fdir@^6.4.4:
  version "6.4.6"
  resolved "https://registry.npmjs.org/fdir/-/fdir-6.4.6.tgz"
  integrity sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==

file-type@^16.5.4:
  version "16.5.4"
  resolved "https://registry.npmjs.org/file-type/-/file-type-16.5.4.tgz"
  integrity sha512-/yFHK0aGjFEgDJjEKP0pWCplsPFPhwyfwevf/pVxiN0tmE4L9LmwWxWukdJSHdoCli4VgQLehjJtwQBnqmsKcw==
  dependencies:
    readable-web-to-node-stream "^3.0.0"
    strtok3 "^6.2.4"
    token-types "^4.1.1"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
  integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
  dependencies:
    to-regex-range "^5.0.1"

flat@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/flat/-/flat-5.0.2.tgz"
  integrity sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==

formidable@^2.0.1:
  version "2.1.5"
  resolved "https://registry.npmjs.org/formidable/-/formidable-2.1.5.tgz"
  integrity sha512-Oz5Hwvwak/DCaXVVUtPn4oLMLLy1CdclLKO1LFgU7XzDpVMUU5UjlSLpGMocyQNNk8F6IJW9M/YdooSn2MRI+Q==
  dependencies:
    "@paralleldrive/cuid2" "^2.2.2"
    dezalgo "^1.0.4"
    once "^1.4.0"
    qs "^6.11.0"

fresh@~0.5.2:
  version "0.5.2"
  resolved "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  integrity sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==

fs-extra@^10.1.0:
  version "10.1.0"
  resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz"
  integrity sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==

get-intrinsic@^1.2.5, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz"
  integrity sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz"
  integrity sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-registry@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/get-registry/-/get-registry-1.2.0.tgz"
  integrity sha512-U4j61Ga4PJfSpDEGAIP2ghy5wcziJ5Cni5EPKJKpJINZMFPiv0WUOXRE+YDPYA1WF/oXOQXQvwq4AU0trfhwcA==
  dependencies:
    which-pm-runs "^1.1.0"

get-stream@^5.1.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz"
  integrity sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

get-tsconfig@^4.7.5:
  version "4.10.1"
  resolved "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.10.1.tgz"
  integrity sha512-auHyJ4AgMz7vgS8Hp3N6HXSmlMdUyhSUrfBF16w153rxtLIEOE+HGqaBppczZvnHLqQJfiHotCYpNhl0lUROFQ==
  dependencies:
    resolve-pkg-maps "^1.0.0"

get-uri@^6.0.1:
  version "6.0.4"
  resolved "https://registry.npmjs.org/get-uri/-/get-uri-6.0.4.tgz"
  integrity sha512-E1b1lFFLvLgak2whF2xDBcOy6NLVGZBqqjJjsIhvopKfWWEi64pLVTWWehV8KlLerZkfNTA95sTe2OdJKm1OzQ==
  dependencies:
    basic-ftp "^5.0.2"
    data-uri-to-buffer "^6.0.2"
    debug "^4.3.4"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

globals@^15.14.0:
  version "15.15.0"
  resolved "https://registry.npmjs.org/globals/-/globals-15.15.0.tgz"
  integrity sha512-7ACyT3wmyp3I61S4fG682L0VA2RGD9otkqGJIwNUMF1SWUombIIk+af1unuDYgMm082aHYwD+mzJvv9Iu8dsgg==

globby@^11.1.0:
  version "11.1.0"
  resolved "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz"
  integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz"
  integrity sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==

graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.11"
  resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

gzip-size@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/gzip-size/-/gzip-size-6.0.0.tgz"
  integrity sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q==
  dependencies:
    duplexer "^0.1.2"

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz"
  integrity sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

http-assert@^1.3.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/http-assert/-/http-assert-1.5.0.tgz"
  integrity sha512-uPpH7OKX4H25hBmU6G1jWNaqJGpTXxey+YOUizJUAgu0AjLUeC8D73hTrhvDS5D+GJN1DN1+hhc/eF/wpxtp0w==
  dependencies:
    deep-equal "~1.0.1"
    http-errors "~1.8.0"

http-errors@^1.6.3, http-errors@^1.7.3, http-errors@~1.8.0:
  version "1.8.1"
  resolved "https://registry.npmjs.org/http-errors/-/http-errors-1.8.1.tgz"
  integrity sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g==
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.1"

http-errors@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz"
  integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-proxy-agent@^7.0.0, http-proxy-agent@^7.0.1, http-proxy-agent@^7.0.2:
  version "7.0.2"
  resolved "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz"
  integrity sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==
  dependencies:
    agent-base "^7.1.0"
    debug "^4.3.4"

https-proxy-agent@^7.0.4, https-proxy-agent@^7.0.6:
  version "7.0.6"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz"
  integrity sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==
  dependencies:
    agent-base "^7.1.2"
    debug "4"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz"
  integrity sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==

iconv-lite@0.4.24:
  version "0.4.24"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ieee754@^1.1.13, ieee754@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

ignore@^5.2.0:
  version "5.3.2"
  resolved "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz"
  integrity sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==

immutable@^5.0.2:
  version "5.1.3"
  resolved "https://registry.npmjs.org/immutable/-/immutable-5.1.3.tgz"
  integrity sha512-+chQdDfvscSF1SJqv2gn4SRO2ZyS3xL3r7IW/wWEEzrzLisnOlKiQu5ytC/BVNcS15C39WT2Hg/bjKjDMcu+zg==

importx@^0.5.1:
  version "0.5.2"
  resolved "https://registry.npmjs.org/importx/-/importx-0.5.2.tgz"
  integrity sha512-YEwlK86Ml5WiTxN/ECUYC5U7jd1CisAVw7ya4i9ZppBoHfFkT2+hChhr3PE2fYxUKLkNyivxEQpa5Ruil1LJBQ==
  dependencies:
    bundle-require "^5.1.0"
    debug "^4.4.0"
    esbuild "^0.20.2 || ^0.21.0 || ^0.22.0 || ^0.23.0 || ^0.24.0 || ^0.25.0"
    jiti "^2.4.2"
    pathe "^2.0.3"
    tsx "^4.19.2"

inaba@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/inaba/-/inaba-1.1.1.tgz"
  integrity sha512-VYgrcz9EwjHELNU74R/p81U/G00u8KuFzao43pyNp7UZix+NY78eUzBy1Ks0tSgxgia+luJMvTD67vV02pk9yg==

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz"
  integrity sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==

inflation@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/inflation/-/inflation-2.1.0.tgz"
  integrity sha512-t54PPJHG1Pp7VQvxyVCJ9mBbjG3Hqryges9bXoOO6GExCPa+//i/d5GSuFtpx3ALLd7lgIAur6zrIlBQyJuMlQ==

inherits@^2.0.3, inherits@^2.0.4, inherits@2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

ip-address@^9.0.5:
  version "9.0.5"
  resolved "https://registry.npmjs.org/ip-address/-/ip-address-9.0.5.tgz"
  integrity sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==
  dependencies:
    jsbn "1.1.0"
    sprintf-js "^1.1.3"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz"
  integrity sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-generator-function@^1.0.7:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.1.0.tgz"
  integrity sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.0"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/is-interactive/-/is-interactive-1.0.0.tgz"
  integrity sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-plain-obj@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-2.1.0.tgz"
  integrity sha512-YWnfyRwxL/+SsrWYfOpUtz5b3YD+nyfkHvjbcanzk8zgyO4ASD67uVMRt8k5bM4lLMDnXfriRhOpemw+NfT1eA==

is-regex@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz"
  integrity sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
  integrity sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==

is-wsl@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz"
  integrity sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==
  dependencies:
    is-docker "^2.0.0"

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

jiti@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmjs.org/jiti/-/jiti-2.4.2.tgz"
  integrity sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@^4.1.0, js-yaml@4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

jsbn@1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/jsbn/-/jsbn-1.1.0.tgz"
  integrity sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==

json5@^2.2.3:
  version "2.2.3"
  resolved "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

keygrip@~1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/keygrip/-/keygrip-1.1.0.tgz"
  integrity sha512-iYSchDJ+liQ8iwbSI2QqsQOvqv58eJCEanyJPJi+Khyu8smkcKSFUCbPwzFcL7YVtZ6eONjqRX/38caJ7QjRAQ==
  dependencies:
    tsscmp "1.0.6"

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz"
  integrity sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==

kleur@^4.1.5:
  version "4.1.5"
  resolved "https://registry.npmjs.org/kleur/-/kleur-4.1.5.tgz"
  integrity sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==

koa-body@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/koa-body/-/koa-body-6.0.1.tgz"
  integrity sha512-M8ZvMD8r+kPHy28aWP9VxL7kY8oPWA+C7ZgCljrCMeaU7uX6wsIQgDHskyrAr9sw+jqnIXyv4Mlxri5R4InIJg==
  dependencies:
    "@types/co-body" "^6.1.0"
    "@types/formidable" "^2.0.5"
    "@types/koa" "^2.13.5"
    co-body "^6.1.0"
    formidable "^2.0.1"
    zod "^3.19.1"

koa-compose@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/koa-compose/-/koa-compose-4.1.0.tgz"
  integrity sha512-8ODW8TrDuMYvXRwra/Kh7/rJo9BtOfPc6qO8eAfC80CnCvSjSl0bkRM24X6/XBBEyj0v1nRUQ1LyOy3dbqOWXw==

koa-convert@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/koa-convert/-/koa-convert-2.0.0.tgz"
  integrity sha512-asOvN6bFlSnxewce2e/DK3p4tltyfC4VM7ZwuTuepI7dEQVcvpyFuBcEARu1+Hxg8DIwytce2n7jrZtRlPrARA==
  dependencies:
    co "^4.6.0"
    koa-compose "^4.1.0"

koa@^2.15.2:
  version "2.16.1"
  resolved "https://registry.npmjs.org/koa/-/koa-2.16.1.tgz"
  integrity sha512-umfX9d3iuSxTQP4pnzLOz0HKnPg0FaUUIKcye2lOiz3KPu1Y3M3xlz76dISdFPQs37P9eJz1wUpcTS6KDPn9fA==
  dependencies:
    accepts "^1.3.5"
    cache-content-type "^1.0.0"
    content-disposition "~0.5.2"
    content-type "^1.0.4"
    cookies "~0.9.0"
    debug "^4.3.2"
    delegates "^1.0.0"
    depd "^2.0.0"
    destroy "^1.0.4"
    encodeurl "^1.0.2"
    escape-html "^1.0.3"
    fresh "~0.5.2"
    http-assert "^1.3.0"
    http-errors "^1.6.3"
    is-generator-function "^1.0.7"
    koa-compose "^4.1.0"
    koa-convert "^2.0.0"
    on-finished "^2.3.0"
    only "~0.0.2"
    parseurl "^1.3.2"
    statuses "^1.5.0"
    type-is "^1.6.16"
    vary "^1.1.2"

koishi-plugin-adapter-onebot@6.8.0:
  version "6.8.0"
  resolved "https://registry.npmjs.org/koishi-plugin-adapter-onebot/-/koishi-plugin-adapter-onebot-6.8.0.tgz"
  integrity sha512-jFPZcINZSlWyKab3xcN7YGUCjyhVfeswgfdmUllvTA4nHO3udPRs9VNmaR7wLfcFSFZYrrJwZvM/t0Aak991sw==
  dependencies:
    qface "^1.4.1"

koishi-plugin-android@^0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/koishi-plugin-android/-/koishi-plugin-android-0.0.1.tgz"
  integrity sha512-Y/sF1IUFjh2qPzkQ8EuA8o6amr0uTTEnx1aMKdtW2mxctVvgPwHhd91SNpzgBpbYTRdn1V2mcFadKXPXT17IKw==

koishi-plugin-assets-local@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmjs.org/koishi-plugin-assets-local/-/koishi-plugin-assets-local-3.3.2.tgz"
  integrity sha512-6oK/yXHnpD1GVxtipXqT9KD5R5aSWvCEDi8I9y5LKy2eKf2De3TCdzx+6d/z7SGHn9+IHt/TL31v+oNQCAmGRw==
  dependencies:
    "@koishijs/assets" "^1.1.2"
    file-type "^16.5.4"

"koishi-plugin-auto-tata-group-name@file:C:\\Users\\<USER>\\Desktop\\auto-tata-group-name\\koishi-app\\external\\auto-tata-group-name":
  version "1.2.3"
  resolved "file:external/auto-tata-group-name"

koishi-plugin-dataview@^2.6.1:
  version "2.7.8"
  resolved "https://registry.npmjs.org/koishi-plugin-dataview/-/koishi-plugin-dataview-2.7.8.tgz"
  integrity sha512-uMWa44aaXXu3GGTVj5wQKMinXbdt8/BREiPdEw5Nso0HW9I5CKfjdcO6CFI2+Hy6eBzEFzrST+aKgsm34/GKYA==
  dependencies:
    "@koishijs/console" "^5.29.2"

koishi-plugin-desktop@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/koishi-plugin-desktop/-/koishi-plugin-desktop-1.0.0.tgz"
  integrity sha512-s99lpcs2MORKFBB+/88c3ImsLs83XCJMUnMC+ljucdEvC/T5nveKUouuu5thB2YqtSsJmpUTEDAngbfw60VGJg==

koishi-plugin-puppeteer@^3.9.0:
  version "3.9.0"
  resolved "https://registry.npmjs.org/koishi-plugin-puppeteer/-/koishi-plugin-puppeteer-3.9.0.tgz"
  integrity sha512-dr2Efo6iiwHSDt/cLbR39RrkOm3GYjai9GVGUsjlKtjP7lvAfGhHHvMBKoVDvsojRIfwPOPxFbQk9KLe7Jb5wA==
  dependencies:
    "@koishijs/canvas" "^0.2.0"
    puppeteer-core "^22.6.1"
    puppeteer-finder "^1.1.1"

koishi-plugin-rate-limit@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/koishi-plugin-rate-limit/-/koishi-plugin-rate-limit-2.0.4.tgz"
  integrity sha512-a4ea1Y4sjYAXrPoTKQN05mrNZ0CblIR2fta94Qp0S5VbMij6N0q0/Rys3Nxh1K+rWkmGd9JE16yuBaNZspa0AA==

"koishi-plugin-repeater-tata@file:C:\\Users\\<USER>\\Desktop\\auto-tata-group-name\\koishi-app\\external\\koishi-plugin-repeater-tata":
  version "1.0.0"
  resolved "file:external/koishi-plugin-repeater-tata"

koishi-plugin-telemetry@^0.2.3:
  version "0.2.3"
  resolved "https://registry.npmjs.org/koishi-plugin-telemetry/-/koishi-plugin-telemetry-0.2.3.tgz"
  integrity sha512-eiX9+gjolHqtnh3vaTCCmoBQkH7M0KaNwItZV7CaF154idr/HDJmqfAN1tDACZr+uwD41bIR8t2e/VZmyHz+UA==

koishi-plugin-theme-vanilla@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/koishi-plugin-theme-vanilla/-/koishi-plugin-theme-vanilla-1.1.0.tgz"
  integrity sha512-lAl9+sAWCUNKhP7PuFUD+kSELpuww+Gt5PWqiJmvq5JtBbr/oh3QGkxYMe5QQWUiDAh4fxuU6Baj7sWR2IGBiQ==

koishi@^4.0.0, koishi@^4.15.0, koishi@^4.15.5, koishi@^4.15.7, koishi@^4.16.0, koishi@^4.16.2, koishi@^4.16.3, koishi@^4.16.4, koishi@^4.16.6, koishi@^4.16.7, koishi@^4.17.10, koishi@^4.17.11, koishi@^4.17.12, koishi@^4.17.3, koishi@^4.17.4, koishi@^4.17.7, koishi@^4.17.9, koishi@^4.18.0, koishi@^4.18.3, koishi@^4.18.4, koishi@^4.18.5, koishi@^4.18.6, koishi@^4.18.7, koishi@^4.18.8:
  version "4.18.8"
  resolved "https://registry.npmjs.org/koishi/-/koishi-4.18.8.tgz"
  integrity sha512-+bTUCy9bktd1NfHhx+D4k7K65ZTg0XjnJXkdsxBxpM5/VbX+ZPPKhV79G9AO58VmMGbFd0rewWPaaH05Zx/HAg==
  dependencies:
    "@koishijs/core" "4.18.8"
    "@koishijs/loader" "4.6.8"
    "@koishijs/plugin-http" "^0.6.3"
    "@koishijs/plugin-proxy-agent" "^0.3.3"
    "@koishijs/plugin-server" "^3.2.7"
    "@koishijs/utils" "^7.2.1"
    "@satorijs/core" "^4.5.1"
    cac "^6.7.14"
    kleur "^4.1.5"

kolorist@^1.8.0:
  version "1.8.0"
  resolved "https://registry.npmjs.org/kolorist/-/kolorist-1.8.0.tgz"
  integrity sha512-Y+60/zizpJ3HRH8DCss+q95yr6145JXZo46OTpFvDZWLfRCE4qChOyk1b26nMaNpfHHgxagk9dXT5OP0Tfe+dQ==

load-tsconfig@^0.2.3:
  version "0.2.5"
  resolved "https://registry.npmjs.org/load-tsconfig/-/load-tsconfig-0.2.5.tgz"
  integrity sha512-IXO6OCs9yg8tMKzfPZ1YmheJbZCiEsnBdcB03l0OcfK9prKnJb96siuHCr5Fl37/yo9DnKU+TLpxzTUspw9shg==

local-pkg@^1.0.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/local-pkg/-/local-pkg-1.1.1.tgz"
  integrity sha512-WunYko2W1NcdfAFpuLUoucsgULmgDBRkdxHxWQ7mK0cQqwPiy8E1enjuRBrhLtZkB5iScJ1XIPdhVEFK8aOLSg==
  dependencies:
    mlly "^1.7.4"
    pkg-types "^2.0.1"
    quansync "^0.2.8"

lodash-es@*, lodash-es@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz"
  integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==

lodash-unified@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/lodash-unified/-/lodash-unified-1.0.3.tgz"
  integrity sha512-WK9qSozxXOD7ZJQlpSqOT+om2ZfcT4yO+03FuzAHD0wF6S0l0090LRPDx3vhTTLZ8cFKpBn+IOcVXK6qOcIlfQ==

lodash@*, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

log-symbols@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz"
  integrity sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

lru-cache@^7.14.1:
  version "7.18.3"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz"
  integrity sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA==

magic-string@^0.30.17:
  version "0.30.17"
  resolved "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz"
  integrity sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

marked-vue@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/marked-vue/-/marked-vue-1.3.0.tgz"
  integrity sha512-SQuFWX7/cwG9BtWVA70LcpOA6ONr2NOodCbS4d81tY3l/ikrtPy7pHJUX06WtCFNQX9hoGovOSbFBBW8ChGjVA==
  dependencies:
    marked "^9.1.6"
    xss "^1.0.14"

marked@^9.1.6:
  version "9.1.6"
  resolved "https://registry.npmjs.org/marked/-/marked-9.1.6.tgz"
  integrity sha512-jcByLnIFkd5gSXZmjNvS1TlmRhCXZjIzHYlaGkPlLIekG55JDR2Z4va9tZwCiP+/RDERiNhMOFu01xd6O5ct1Q==

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz"
  integrity sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==

mdn-data@2.12.2:
  version "2.12.2"
  resolved "https://registry.npmjs.org/mdn-data/-/mdn-data-2.12.2.tgz"
  integrity sha512-IEn+pegP1aManZuckezWCO+XZQDplx1366JoVhTpMpBB1sPey/SbveZQUosKiKiGYjg1wH4pMlNgXbCiYgihQA==

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
  integrity sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==

memoize-one@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/memoize-one/-/memoize-one-6.0.0.tgz"
  integrity sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

methods@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  integrity sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==

micromatch@^4.0.5, micromatch@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz"
  integrity sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@^1.52.0:
  version "1.54.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz"
  integrity sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.18, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

minato@^3.6.0, minato@^3.6.1:
  version "3.6.1"
  resolved "https://registry.npmjs.org/minato/-/minato-3.6.1.tgz"
  integrity sha512-4VbFd9TqNUvd/okegSZUaxWZSia5yfNXlfJH/pPxi0HURld5sikGfNh+nvpYfUuAclAt960M7dVG/Z634muzhg==
  dependencies:
    cosmokit "^1.6.3"

mitt@3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/mitt/-/mitt-3.0.1.tgz"
  integrity sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==

mlly@^1.7.4:
  version "1.7.4"
  resolved "https://registry.npmjs.org/mlly/-/mlly-1.7.4.tgz"
  integrity sha512-qmdSIPC4bDJXgZTCR7XosJiNKySV7O215tsPtDN9iEO/7q/76b/ijtgRu/+epFXSJhijtTCCGp3DWS549P3xKw==
  dependencies:
    acorn "^8.14.0"
    pathe "^2.0.1"
    pkg-types "^1.3.0"
    ufo "^1.5.4"

mrmime@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/mrmime/-/mrmime-2.0.1.tgz"
  integrity sha512-Y3wQdFg2Va6etvQ5I82yUhGdsKrcYox6p7FfL1LbK2J4V01F9TGlepTIhnK24t7koZibmg82KGglhA1XK5IsLQ==

ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

nanoid@^3.3.11:
  version "3.3.11"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz"
  integrity sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==

negotiator@0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz"
  integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==

netmask@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/netmask/-/netmask-2.0.2.tgz"
  integrity sha512-dBpDMdxv9Irdq66304OLfEmQ9tbNRFnFTuZiLo+bD+r332bBmMJ8GBLXklIXXgxd3+v9+KUnZaUR5PJMa75Gsg==

node-addon-api@^7.0.0:
  version "7.1.1"
  resolved "https://registry.npmjs.org/node-addon-api/-/node-addon-api-7.1.1.tgz"
  integrity sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==

node-fetch-native@^1.6.4:
  version "1.6.6"
  resolved "https://registry.npmjs.org/node-fetch-native/-/node-fetch-native-1.6.6.tgz"
  integrity sha512-8Mc2HhqPdlIfedsuZoc3yioPuzp6b+L5jRCRY1QzuWZh2EGJVQrGppC6V6cF0bLdbW0+O2YpqCA25aF/1lvipQ==

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-wheel-es@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz"
  integrity sha512-Wj7+EJQ8mSuXr2iWfnujrimU35R2W4FAErEyTmJoJ7ucwTn2hOUSsRehMb5RSYkxXGTM7Y9QpvPmp++w5ftoJw==

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz"
  integrity sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==
  dependencies:
    path-key "^3.0.0"

ns-require@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/ns-require/-/ns-require-1.1.4.tgz"
  integrity sha512-Zk25pQj4u5i6DS0vaNO5aSSXewybVqqVVjz8AOxFy9DNPtmu3jlexMz6kUXLV2oB+X6iQeAnHXSzj5Qz/IeDaQ==

object-inspect@^1.13.1, object-inspect@^1.13.3:
  version "1.13.4"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz"
  integrity sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==

ofetch@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/ofetch/-/ofetch-1.4.1.tgz"
  integrity sha512-QZj2DfGplQAr2oj9KzceK9Hwz6Whxazmn85yYeVuS3u9XTMOGMRx0kO95MQ+vLsj/S/NwBDMMLU5hpxvI6Tklw==
  dependencies:
    destr "^2.0.3"
    node-fetch-native "^1.6.4"
    ufo "^1.5.4"

on-finished@^2.3.0:
  version "2.4.1"
  resolved "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz"
  integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
  dependencies:
    ee-first "1.1.1"

once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
  integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
  dependencies:
    mimic-fn "^2.1.0"

only@~0.0.2:
  version "0.0.2"
  resolved "https://registry.npmjs.org/only/-/only-0.0.2.tgz"
  integrity sha512-Fvw+Jemq5fjjyWz6CpKx6w9s7xxqo3+JCyM0WXWeCSOboZ8ABkyvP8ID4CZuChA/wxSx+XSJmdOm8rGVyJ1hdQ==

open@^8.4.2:
  version "8.4.2"
  resolved "https://registry.npmjs.org/open/-/open-8.4.2.tgz"
  integrity sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

ora@^5.4.1:
  version "5.4.1"
  resolved "https://registry.npmjs.org/ora/-/ora-5.4.1.tgz"
  integrity sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

p-map@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/p-map/-/p-map-4.0.0.tgz"
  integrity sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==
  dependencies:
    aggregate-error "^3.0.0"

pac-proxy-agent@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-7.2.0.tgz"
  integrity sha512-TEB8ESquiLMc0lV8vcd5Ql/JAKAoyzHFXaStwjkzpOpC5Yv+pIzLfHvjTSdf3vpa2bMiUQrg9i6276yn8666aA==
  dependencies:
    "@tootallnate/quickjs-emscripten" "^0.23.0"
    agent-base "^7.1.2"
    debug "^4.3.4"
    get-uri "^6.0.1"
    http-proxy-agent "^7.0.0"
    https-proxy-agent "^7.0.6"
    pac-resolver "^7.0.1"
    socks-proxy-agent "^8.0.5"

pac-resolver@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/pac-resolver/-/pac-resolver-7.0.1.tgz"
  integrity sha512-5NPgf87AT2STgwa2ntRMr45jTKrYBGkVU36yT0ig/n/GMAa3oPqhZfIQ2kMEimReg0+t9kZViDVZ83qfVUlckg==
  dependencies:
    degenerator "^5.0.0"
    netmask "^2.0.2"

package-manager-detector@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/package-manager-detector/-/package-manager-detector-1.3.0.tgz"
  integrity sha512-ZsEbbZORsyHuO00lY1kV3/t72yp6Ysay6Pd17ZAlNGuGwmWDLCJxFpRs0IzfXfj1o4icJOkUEioexFHzyPurSQ==

parseurl@^1.3.2, parseurl@^1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-to-regexp@^6.1.0, path-to-regexp@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-6.3.0.tgz"
  integrity sha512-Yhpw4T9C6hPpgPeA28us07OJeqZ5EzQTkbfwuhsUg0c237RomFoETJgmp2sa3F/41gfLE6G5cqcYwznmeEeOlQ==

path-to-regexp@^8.2.0:
  version "8.2.0"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.2.0.tgz"
  integrity sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ==

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==

pathe@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/pathe/-/pathe-1.1.2.tgz"
  integrity sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==

pathe@^2.0.1:
  version "2.0.3"
  resolved "https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz"
  integrity sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==

pathe@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz"
  integrity sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==

peek-readable@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/peek-readable/-/peek-readable-4.1.0.tgz"
  integrity sha512-ZI3LnwUv5nOGbQzD9c2iDG6toheuXSZP5esSHBjopsXH4dg19soufvpUGA3uohi5anFtGb2lhAVdHzH6R/Evvg==

pend@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz"
  integrity sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==

perfect-debounce@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/perfect-debounce/-/perfect-debounce-1.0.0.tgz"
  integrity sha512-xCy9V055GLEqoFaHoC1SoLIaLmWctgCUaBaWxDZ7/Zx4CTyX7cJQLJOok/orfjZAh9kEYpjJa4d0KcJmCbctZA==

picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  integrity sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

"picomatch@^3 || ^4", picomatch@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz"
  integrity sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==

pkg-types@^1.3.0:
  version "1.3.1"
  resolved "https://registry.npmjs.org/pkg-types/-/pkg-types-1.3.1.tgz"
  integrity sha512-/Jm5M4RvtBFVkKWRu2BLUTNP8/M2a+UwuAX+ae4770q1qVGtfjG+WTCupoZixokjmHiry8uI+dlY8KXYV5HVVQ==
  dependencies:
    confbox "^0.1.8"
    mlly "^1.7.4"
    pathe "^2.0.1"

pkg-types@^2.0.1:
  version "2.2.0"
  resolved "https://registry.npmjs.org/pkg-types/-/pkg-types-2.2.0.tgz"
  integrity sha512-2SM/GZGAEkPp3KWORxQZns4M+WSeXbC2HEvmOIJe3Cmiv6ieAJvdVhDldtHqM5J1Y7MrR1XhkBT/rMlhh9FdqQ==
  dependencies:
    confbox "^0.2.2"
    exsolve "^1.0.7"
    pathe "^2.0.3"

postcss@^8.4.43, postcss@^8.4.49, postcss@^8.5.6:
  version "8.5.6"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz"
  integrity sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==
  dependencies:
    nanoid "^3.3.11"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

process@^0.11.10:
  version "0.11.10"
  resolved "https://registry.npmjs.org/process/-/process-0.11.10.tgz"
  integrity sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==

progress@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz"
  integrity sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==

prompts@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz"
  integrity sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

proxy-agent@^6.4.0:
  version "6.5.0"
  resolved "https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.5.0.tgz"
  integrity sha512-TmatMXdr2KlRiA2CyDu8GqR8EjahTG3aY3nXjdzFyoZbmB8hrBsTyMezhULIXKnC0jpfjlmiZ3+EaCzoInSu/A==
  dependencies:
    agent-base "^7.1.2"
    debug "^4.3.4"
    http-proxy-agent "^7.0.1"
    https-proxy-agent "^7.0.6"
    lru-cache "^7.14.1"
    pac-proxy-agent "^7.1.0"
    proxy-from-env "^1.1.0"
    socks-proxy-agent "^8.0.5"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

pump@^3.0.0:
  version "3.0.3"
  resolved "https://registry.npmjs.org/pump/-/pump-3.0.3.tgz"
  integrity sha512-todwxLMY7/heScKmntwQG8CXVkWUOdYxIvY2s0VWAAMh/nd8SoYiRaKjlr7+iCs984f2P8zvrfWcDDYVb73NfA==
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

puppeteer-core@^22.6.1:
  version "22.15.0"
  resolved "https://registry.npmjs.org/puppeteer-core/-/puppeteer-core-22.15.0.tgz"
  integrity sha512-cHArnywCiAAVXa3t4GGL2vttNxh7GqXtIYGym99egkNJ3oG//wL9LkvO4WE8W1TJe95t1F1ocu9X4xWaGsOKOA==
  dependencies:
    "@puppeteer/browsers" "2.3.0"
    chromium-bidi "0.6.3"
    debug "^4.3.6"
    devtools-protocol "0.0.1312386"
    ws "^8.18.0"

puppeteer-finder@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/puppeteer-finder/-/puppeteer-finder-1.1.1.tgz"
  integrity sha512-mrZsTAkxl+8O2VtpWmEiNvL1IUEqAZQdX4RRdgREaPb45pQr7pj50LJnPKKSDmOxvYKyPje+vS9uI8OJR3u1gQ==

qface@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/qface/-/qface-1.4.1.tgz"
  integrity sha512-52qX9qdiDFd53xnYAFitkXVldcSddd4ZQiFTV2IluM+2HdDiJph3CKtmPi7CTCA9QF7K2d2WUAH3E2Y4P6fEjQ==

qs@^6.11.0, qs@^6.5.2:
  version "6.14.0"
  resolved "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz"
  integrity sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==
  dependencies:
    side-channel "^1.1.0"

quansync@^0.2.8:
  version "0.2.10"
  resolved "https://registry.npmjs.org/quansync/-/quansync-0.2.10.tgz"
  integrity sha512-t41VRkMYbkHyCYmOvx/6URnN80H7k4X0lLdBMGsz+maAwrJQYB1djpV6vHrQIBE0WBSGqhtEHrK9U3DWWH8v7A==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

raw-body@^2.3.3:
  version "2.5.2"
  resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz"
  integrity sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

readable-stream@^3.4.0:
  version "3.6.2"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^4.7.0:
  version "4.7.0"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-4.7.0.tgz"
  integrity sha512-oIGGmcpTLwPga8Bn6/Z75SVaH1z5dUut2ibSyAMVhmUggWpmDn2dapB0n7f8nwaSiRtepAsfJyfXIO5DCVAODg==
  dependencies:
    abort-controller "^3.0.0"
    buffer "^6.0.3"
    events "^3.3.0"
    process "^0.11.10"
    string_decoder "^1.3.0"

readable-web-to-node-stream@^3.0.0:
  version "3.0.4"
  resolved "https://registry.npmjs.org/readable-web-to-node-stream/-/readable-web-to-node-stream-3.0.4.tgz"
  integrity sha512-9nX56alTf5bwXQ3ZDipHJhusu9NTQJ/CVPtb/XHAJCXihZeitfJvIRS4GqQ/mfIoOE3IelHMrpayVrosdHBuLw==
  dependencies:
    readable-stream "^4.7.0"

readdirp@^4.0.1:
  version "4.1.2"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-4.1.2.tgz"
  integrity sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

reggol@^1.7.0, reggol@^1.7.1:
  version "1.7.1"
  resolved "https://registry.npmjs.org/reggol/-/reggol-1.7.1.tgz"
  integrity sha512-0Vr1vRYDFnsA14BipkHRn3+4FxlZxQJ4VQG2pFE2obSrR68hTHVbbQzS3/ZAHNb6l/+ThaJrc5BPgrYDM3yNyg==
  dependencies:
    cosmokit "^1.6.3"
    object-inspect "^1.13.1"
    supports-color "^8.1.1"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==

resolve-pkg-maps@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/resolve-pkg-maps/-/resolve-pkg-maps-1.0.0.tgz"
  integrity sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz"
  integrity sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

reusify@^1.0.4:
  version "1.1.0"
  resolved "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz"
  integrity sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==

rollup@^1.20.0||^2.0.0||^3.0.0||^4.0.0, rollup@^4.20.0:
  version "4.44.1"
  resolved "https://registry.npmjs.org/rollup/-/rollup-4.44.1.tgz"
  integrity sha512-x8H8aPvD+xbl0Do8oez5f5o8eMS3trfCghc4HhLAnCkj7Vl0d1JWGs0UF/D886zLW2rOj2QymV/JcSSsw+XDNg==
  dependencies:
    "@types/estree" "1.0.8"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.44.1"
    "@rollup/rollup-android-arm64" "4.44.1"
    "@rollup/rollup-darwin-arm64" "4.44.1"
    "@rollup/rollup-darwin-x64" "4.44.1"
    "@rollup/rollup-freebsd-arm64" "4.44.1"
    "@rollup/rollup-freebsd-x64" "4.44.1"
    "@rollup/rollup-linux-arm-gnueabihf" "4.44.1"
    "@rollup/rollup-linux-arm-musleabihf" "4.44.1"
    "@rollup/rollup-linux-arm64-gnu" "4.44.1"
    "@rollup/rollup-linux-arm64-musl" "4.44.1"
    "@rollup/rollup-linux-loongarch64-gnu" "4.44.1"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.44.1"
    "@rollup/rollup-linux-riscv64-gnu" "4.44.1"
    "@rollup/rollup-linux-riscv64-musl" "4.44.1"
    "@rollup/rollup-linux-s390x-gnu" "4.44.1"
    "@rollup/rollup-linux-x64-gnu" "4.44.1"
    "@rollup/rollup-linux-x64-musl" "4.44.1"
    "@rollup/rollup-win32-arm64-msvc" "4.44.1"
    "@rollup/rollup-win32-ia32-msvc" "4.44.1"
    "@rollup/rollup-win32-x64-msvc" "4.44.1"
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@~5.2.0, safe-buffer@5.2.1:
  version "5.2.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

safe-regex-test@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.1.0.tgz"
  integrity sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

sass@*, sass@^1.82.0:
  version "1.89.2"
  resolved "https://registry.npmjs.org/sass/-/sass-1.89.2.tgz"
  integrity sha512-xCmtksBKd/jdJ9Bt9p7nPKiuqrlBMBuuGkQlkhZjjQk3Ty48lv93k5Dq6OPkKt4XwxDJ7tvlfrTa1MPA9bf+QA==
  dependencies:
    chokidar "^4.0.0"
    immutable "^5.0.2"
    source-map-js ">=0.6.2 <2.0.0"
  optionalDependencies:
    "@parcel/watcher" "^2.4.1"

schemastery-vue@^7.3.13:
  version "7.3.13"
  resolved "https://registry.npmjs.org/schemastery-vue/-/schemastery-vue-7.3.13.tgz"
  integrity sha512-ccVbH+cOvvI8kjuQhik/gYD4Mlu3jm4QD7Udpbmn040/eSOPtiVrTtR/jTO71BNqkOQUSLiIiedWk/J0feekWA==
  dependencies:
    schemastery "^3.16.1"

schemastery@^3.14.6, schemastery@^3.16.1:
  version "3.16.1"
  resolved "https://registry.npmjs.org/schemastery/-/schemastery-3.16.1.tgz"
  integrity sha512-SohCvJCqhdhCf4CTLrZjgs5ZgvrXVl/I46zgfwlu5ouUWi9knY2amXlVqEbCUkiV6W1VhdiITi/LqVeAWGF2kA==
  dependencies:
    cosmokit "^1.7.2"

semver@^7.5.4, semver@^7.6.3:
  version "7.7.2"
  resolved "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz"
  integrity sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
  integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz"
  integrity sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz"
  integrity sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz"
  integrity sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz"
  integrity sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^3.0.2, signal-exit@^3.0.3:
  version "3.0.7"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

sirv@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/sirv/-/sirv-3.0.1.tgz"
  integrity sha512-FoqMu0NCGBLCcAkS1qA+XJIQTR6/JHfQXl+uGteNCQ76T91DMUjPa9xfmeqMY3z80nLSg9yQmNjK0Px6RWsH/A==
  dependencies:
    "@polka/url" "^1.0.0-next.24"
    mrmime "^2.0.0"
    totalist "^3.0.0"

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz"
  integrity sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
  integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==

smart-buffer@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz"
  integrity sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==

socks-proxy-agent@^8.0.2, socks-proxy-agent@^8.0.5:
  version "8.0.5"
  resolved "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-8.0.5.tgz"
  integrity sha512-HehCEsotFqbPW9sJ8WVYB6UbmIMv7kUUORIF2Nncq4VQvBfNBLibW9YZR5dlYCSUhwcD628pRllm7n+E+YTzJw==
  dependencies:
    agent-base "^7.1.2"
    debug "^4.3.4"
    socks "^2.8.3"

socks@^2.8.1, socks@^2.8.3:
  version "2.8.5"
  resolved "https://registry.npmjs.org/socks/-/socks-2.8.5.tgz"
  integrity sha512-iF+tNDQla22geJdTyJB1wM/qrX9DMRwWrciEPwWLPRWAUEM8sQiyxgckLxWT1f7+9VabJS0jTGGr4QgBuvi6Ww==
  dependencies:
    ip-address "^9.0.5"
    smart-buffer "^4.2.0"

source-map-js@^1.0.1, source-map-js@^1.0.2, source-map-js@^1.2.1, "source-map-js@>=0.6.2 <2.0.0":
  version "1.2.1"
  resolved "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"
  integrity sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==

source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

sprintf-js@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.3.tgz"
  integrity sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==

statuses@^1.5.0, "statuses@>= 1.5.0 < 2":
  version "1.5.0"
  resolved "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz"
  integrity sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==

statuses@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz"
  integrity sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==

streamx@^2.15.0, streamx@^2.21.0:
  version "2.22.1"
  resolved "https://registry.npmjs.org/streamx/-/streamx-2.22.1.tgz"
  integrity sha512-znKXEBxfatz2GBNK02kRnCXjV+AA4kjZIUxeWSr3UGirZMJfTE9uiwKHobnbgxWyL/JWro8tTq+vOqAK1/qbSA==
  dependencies:
    fast-fifo "^1.3.2"
    text-decoder "^1.1.0"
  optionalDependencies:
    bare-events "^2.2.0"

string_decoder@^1.1.1, string_decoder@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==

strtok3@^6.2.4:
  version "6.3.0"
  resolved "https://registry.npmjs.org/strtok3/-/strtok3-6.3.0.tgz"
  integrity sha512-fZtbhtvI9I48xDSywd/somNqgUHl2L2cstmXCCif0itOf96jeW18MBSyrLuNicYQVkvpOxkZtkzujiTJ9LW5Jw==
  dependencies:
    "@tokenizer/token" "^0.3.0"
    peek-readable "^4.1.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.1.1:
  version "8.1.1"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
  integrity sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==
  dependencies:
    has-flag "^4.0.0"

tar-fs@^3.0.6:
  version "3.1.0"
  resolved "https://registry.npmjs.org/tar-fs/-/tar-fs-3.1.0.tgz"
  integrity sha512-5Mty5y/sOF1YWj1J6GiBodjlDc05CUR8PKXrsnFAiSG0xA+GHeWLovaZPYUDXkH/1iKRf2+M5+OrRgzC7O9b7w==
  dependencies:
    pump "^3.0.0"
    tar-stream "^3.1.5"
  optionalDependencies:
    bare-fs "^4.0.1"
    bare-path "^3.0.0"

tar-stream@^3.1.5:
  version "3.1.7"
  resolved "https://registry.npmjs.org/tar-stream/-/tar-stream-3.1.7.tgz"
  integrity sha512-qJj60CXt7IU1Ffyc3NJMjh6EkuCFej46zUqJ4J7pqYlThyd9bO0XBTmcOIhSzZJVWfsLks0+nle/j538YAW9RQ==
  dependencies:
    b4a "^1.6.4"
    fast-fifo "^1.2.0"
    streamx "^2.15.0"

text-decoder@^1.1.0:
  version "1.2.3"
  resolved "https://registry.npmjs.org/text-decoder/-/text-decoder-1.2.3.tgz"
  integrity sha512-3/o9z3X0X0fTupwsYvR03pJ/DjWuqqrfwBgTQzdWDiQSm9KitAyz/9WqsT2JQW7KV2m+bC2ol/zqpW37NHxLaA==
  dependencies:
    b4a "^1.6.4"

throttle-debounce@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/throttle-debounce/-/throttle-debounce-3.0.1.tgz"
  integrity sha512-dTEWWNu6JmeVXY0ZYoPuH5cRIwc0MeGbJwah9KUNYSJwommQpCzTySTpEe8Gs1J23aeWEuAobe4Ag7EHVt/LOg==

through@^2.3.8:
  version "2.3.8"
  resolved "https://registry.npmjs.org/through/-/through-2.3.8.tgz"
  integrity sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==

tinyexec@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/tinyexec/-/tinyexec-1.0.1.tgz"
  integrity sha512-5uC6DDlmeqiOwCPmK9jMSdOuZTh8bU39Ys6yidB+UTt5hfZUPGAypSgFRiEp+jbi9qH40BLDvy85jIU88wKSqw==

tinyglobby@^0.2.10:
  version "0.2.14"
  resolved "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.14.tgz"
  integrity sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==
  dependencies:
    fdir "^6.4.4"
    picomatch "^4.0.2"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
  integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==

token-types@^4.1.1:
  version "4.2.1"
  resolved "https://registry.npmjs.org/token-types/-/token-types-4.2.1.tgz"
  integrity sha512-6udB24Q737UD/SDsKAHI9FCRP7Bqc9D/MQUV02ORQg5iskjtLJlZJNdN4kKtcdtwCeWIwIHDGaUsTsCCAa8sFQ==
  dependencies:
    "@tokenizer/token" "^0.3.0"
    ieee754 "^1.2.1"

tosource@2.0.0-alpha.3:
  version "2.0.0-alpha.3"
  resolved "https://registry.npmjs.org/tosource/-/tosource-2.0.0-alpha.3.tgz"
  integrity sha512-KAB2lrSS48y91MzFPFuDg4hLbvDiyTjOVgaK7Erw+5AmZXNq4sFRVn8r6yxSLuNs15PaokrDRpS61ERY9uZOug==

totalist@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/totalist/-/totalist-3.0.1.tgz"
  integrity sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==

tsconfig-utils@^4.0.5, tsconfig-utils@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/tsconfig-utils/-/tsconfig-utils-4.1.1.tgz"
  integrity sha512-iEHJnGeDschUpFxnuIDnIlTvdla58xofi9Gp8EQI537c9ql35daTWk0VTdXJOwjSuE+AbrQB5EDm7Fv0imI8qw==
  dependencies:
    json5 "^2.2.3"

tslib@^2.0.1:
  version "2.8.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

tsscmp@1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/tsscmp/-/tsscmp-1.0.6.tgz"
  integrity sha512-LxhtAkPDTkVCMQjt2h6eBVY28KCjikZqZfMcC15YBeNjkgUpdCfBu5HoiOTDu86v6smE8yOjyEktJ8hlbANHQA==

tsx@^4.16.2, tsx@^4.19.2:
  version "4.20.3"
  resolved "https://registry.npmjs.org/tsx/-/tsx-4.20.3.tgz"
  integrity sha512-qjbnuR9Tr+FJOMBqJCW5ehvIo/buZq7vH7qD7JziU98h6l3qGy0a/yPFjwO+y0/T7GFpNgNAvEcPPVfyT8rrPQ==
  dependencies:
    esbuild "~0.25.0"
    get-tsconfig "^4.7.5"
  optionalDependencies:
    fsevents "~2.3.3"

type-is@^1.6.16:
  version "1.6.18"
  resolved "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
  integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typescript@*, typescript@^5.0.0, typescript@^5.6.2:
  version "5.8.3"
  resolved "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz"
  integrity sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==

ufo@^1.5.4:
  version "1.6.1"
  resolved "https://registry.npmjs.org/ufo/-/ufo-1.6.1.tgz"
  integrity sha512-9a4/uxlTWJ4+a5i0ooc1rU7C7YOw3wT+UGqdeNNHWnOF9qcMBgLRS+4IYUqbczewFx4mLEig6gawh7X6mFlEkA==

unbzip2-stream@^1.4.3:
  version "1.4.3"
  resolved "https://registry.npmjs.org/unbzip2-stream/-/unbzip2-stream-1.4.3.tgz"
  integrity sha512-mlExGW4w71ebDJviH16lQLtZS32VKqsSfk80GCfUlwT/4/hNRFsoscrF/c++9xinkMzECL1uL9DDwXqFWkruPg==
  dependencies:
    buffer "^5.2.1"
    through "^2.3.8"

unconfig@~0.6.0:
  version "0.6.1"
  resolved "https://registry.npmjs.org/unconfig/-/unconfig-0.6.1.tgz"
  integrity sha512-cVU+/sPloZqOyJEAfNwnQSFCzFrZm85vcVkryH7lnlB/PiTycUkAjt5Ds79cfIshGOZ+M5v3PBDnKgpmlE5DtA==
  dependencies:
    "@antfu/utils" "^8.1.0"
    defu "^6.1.4"
    importx "^0.5.1"

undici-types@~6.21.0:
  version "6.21.0"
  resolved "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz"
  integrity sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==

undici@^6.10.1:
  version "6.21.3"
  resolved "https://registry.npmjs.org/undici/-/undici-6.21.3.tgz"
  integrity sha512-gBLkYIlEnSp8pFbT64yFgGE6UIB9tAkhukC23PmMDCe5Nd+cRqKxSjw5y54MK2AZMgZfJWMaNE4nYUHgi1XEOw==

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz"
  integrity sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==

unocss@^0.65.1:
  version "0.65.4"
  resolved "https://registry.npmjs.org/unocss/-/unocss-0.65.4.tgz"
  integrity sha512-KUCW5OzI20Ik6j1zXkkrpWhxZ59TwSKl6+DvmYHEzMfaEcrHlBZaFSApAoSt2CYSvo6SluGiKyr+Im1UTkd4KA==
  dependencies:
    "@unocss/astro" "0.65.4"
    "@unocss/cli" "0.65.4"
    "@unocss/core" "0.65.4"
    "@unocss/postcss" "0.65.4"
    "@unocss/preset-attributify" "0.65.4"
    "@unocss/preset-icons" "0.65.4"
    "@unocss/preset-mini" "0.65.4"
    "@unocss/preset-tagify" "0.65.4"
    "@unocss/preset-typography" "0.65.4"
    "@unocss/preset-uno" "0.65.4"
    "@unocss/preset-web-fonts" "0.65.4"
    "@unocss/preset-wind" "0.65.4"
    "@unocss/transformer-attributify-jsx" "0.65.4"
    "@unocss/transformer-compile-class" "0.65.4"
    "@unocss/transformer-directives" "0.65.4"
    "@unocss/transformer-variant-group" "0.65.4"
    "@unocss/vite" "0.65.4"

unpipe@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  integrity sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==

urlpattern-polyfill@10.0.0:
  version "10.0.0"
  resolved "https://registry.npmjs.org/urlpattern-polyfill/-/urlpattern-polyfill-10.0.0.tgz"
  integrity sha512-H/A06tKD7sS1O1X2SshBVeA5FLycRpjqiBeqGKmBwBDBy28EnRjORxTNe269KSSr5un5qyWi1iL61wLxpd+ZOg==

util-deprecate@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

vary@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  integrity sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==

"vite@^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0", "vite@^3.2.7 || ^4.0.5 || >=5.0.5", "vite@^5.0.0 || ^6.0.0", vite@^5.4.10:
  version "5.4.19"
  resolved "https://registry.npmjs.org/vite/-/vite-5.4.19.tgz"
  integrity sha512-qO3aKv3HoQC8QKiNSTuUM1l9o/XX3+c+VTgLHbJWHZGeTPVAg2XwazI9UWzoxjIJCGCV2zU60uqMzjeLZuULqA==
  dependencies:
    esbuild "^0.21.3"
    postcss "^8.4.43"
    rollup "^4.20.0"
  optionalDependencies:
    fsevents "~2.3.3"

vue-demi@*:
  version "0.14.10"
  resolved "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.10.tgz"
  integrity sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==

vue-demi@>=0.14.10:
  version "0.14.10"
  resolved "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.10.tgz"
  integrity sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==

vue-flow-layout@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmjs.org/vue-flow-layout/-/vue-flow-layout-0.1.1.tgz"
  integrity sha512-JdgRRUVrN0Y2GosA0M68DEbKlXMqJ7FQgsK8CjQD2vxvNSqAU6PZEpi4cfcTVtfM2GVOMjHo7GKKLbXxOBqDqA==

vue-i18n@^9.10.2:
  version "9.14.4"
  resolved "https://registry.npmjs.org/vue-i18n/-/vue-i18n-9.14.4.tgz"
  integrity sha512-B934C8yUyWLT0EMud3DySrwSUJI7ZNiWYsEEz2gknTthqKiG4dzWE/WSa8AzCuSQzwBEv4HtG1jZDhgzPfWSKQ==
  dependencies:
    "@intlify/core-base" "9.14.4"
    "@intlify/shared" "9.14.4"
    "@vue/devtools-api" "^6.5.0"

vue-router@^4.4.5:
  version "4.5.1"
  resolved "https://registry.npmjs.org/vue-router/-/vue-router-4.5.1.tgz"
  integrity sha512-ogAF3P97NPm8fJsE4by9dwSYtDwXIY1nFY9T6DyQnGHd1E2Da94w9JIolpe42LJGIl0DwOHBi8TcRPlPGwbTtw==
  dependencies:
    "@vue/devtools-api" "^6.6.4"

vue@^3, vue@^3.0.0, "vue@^3.0.0-0 || ^2.6.0", vue@^3.2.0, vue@^3.2.25, vue@^3.4.37, vue@^3.5.12, vue@3.5.17:
  version "3.5.17"
  resolved "https://registry.npmjs.org/vue/-/vue-3.5.17.tgz"
  integrity sha512-LbHV3xPN9BeljML+Xctq4lbz2lVHCR6DtbpTf5XIO6gugpXUN49j2QQPcMj086r9+AkJ0FfUT8xjulKKBkkr9g==
  dependencies:
    "@vue/compiler-dom" "3.5.17"
    "@vue/compiler-sfc" "3.5.17"
    "@vue/runtime-dom" "3.5.17"
    "@vue/server-renderer" "3.5.17"
    "@vue/shared" "3.5.17"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz"
  integrity sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==
  dependencies:
    defaults "^1.0.3"

which-pm-runs@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/which-pm-runs/-/which-pm-runs-1.1.0.tgz"
  integrity sha512-n1brCuqClxfFfq/Rb0ICg9giSZqCS+pLtccdag6C2HyufBrh3fBOiy9nb6ggRMvWOVH5GrdJskj5iGTZNxd7SA==

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

ws@^8.16.0, ws@^8.18.0:
  version "8.18.3"
  resolved "https://registry.npmjs.org/ws/-/ws-8.18.3.tgz"
  integrity sha512-PEIGCY5tSlUt50cqyMXfCzX+oOPqN0vuGqWzbcJ2xvnkzkq46oOpz7dQaTDBdfICb4N14+GARUDw2XV2N4tvzg==

xss@^1.0.14:
  version "1.0.15"
  resolved "https://registry.npmjs.org/xss/-/xss-1.0.15.tgz"
  integrity sha512-FVdlVVC67WOIPvfOwhoMETV72f6GbW7aOabBC3WxN/oUdoEMDyLz4OgRv5/gck2ZeNqEQu+Tb0kloovXOfpYVg==
  dependencies:
    commander "^2.20.3"
    cssfilter "0.0.10"

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz"
  integrity sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==

yakumo-esbuild@^1.0.0-beta.7:
  version "1.0.0"
  resolved "https://registry.npmjs.org/yakumo-esbuild/-/yakumo-esbuild-1.0.0.tgz"
  integrity sha512-3HBvLm16mry/QeCfy2uogtd5zF7GpbEATttQ8HjXsQnflFV1jNeLGrMrEu/HfaGxwBwRT0OJOtktUrt3EjnToQ==
  dependencies:
    dumble "^0.2.1"
    schemastery "^3.16.1"
    tsconfig-utils "^4.1.1"

yakumo-tsc@^1.0.0-beta.5:
  version "1.0.0"
  resolved "https://registry.npmjs.org/yakumo-tsc/-/yakumo-tsc-1.0.0.tgz"
  integrity sha512-GXKTNaCmVVTPgZKbD+Nb3+sbGEXKZeALlRjRkDUxE27Artk+4ny8bwcimbWFvvPJk57LnzCMWqefzNz4jBiBlQ==
  dependencies:
    atsc "^2.1.0"
    dtsc "^3.1.0"
    tsconfig-utils "^4.1.1"

yakumo@^1.0.0, yakumo@^1.0.0-beta.21:
  version "1.0.0"
  resolved "https://registry.npmjs.org/yakumo/-/yakumo-1.0.0.tgz"
  integrity sha512-ayEj07jQR4OsvZlTKstzU8x7X5+ey/3/HR/5+ZPTFnAR0Pc30IPteub87LU3loqli3Kzj+isVHRW1oa1MA8eAw==
  dependencies:
    cordis "^3.18.1"
    cosmokit "^1.7.2"
    detect-indent "^6.1.0"
    execa "^5.1.1"
    get-registry "^1.2.0"
    globby "^11.1.0"
    kleur "^4.1.5"
    ora "^5.4.1"
    p-map "^4.0.0"
    picomatch "^2.3.1"
    prompts "^2.4.2"
    semver "^7.6.3"
    which-pm-runs "^1.1.0"
    yargs-parser "^21.1.1"
    yargs-unparser "^2.0.0"

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz"
  integrity sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==

yargs-unparser@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/yargs-unparser/-/yargs-unparser-2.0.0.tgz"
  integrity sha512-7pRTIA9Qc1caZ0bZ6RYRGbHJthJWuakf+WmHK0rVeLkNrrGhfoabBNdue6kdINI6r4if7ocq9aD/n7xwKOdzOA==
  dependencies:
    camelcase "^6.0.0"
    decamelize "^4.0.0"
    flat "^5.0.2"
    is-plain-obj "^2.1.0"

yargs@^17.7.2:
  version "17.7.2"
  resolved "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz"
  integrity sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yauzl@^2.10.0:
  version "2.10.0"
  resolved "https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz"
  integrity sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer "~1.1.0"

ylru@^1.2.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/ylru/-/ylru-1.4.0.tgz"
  integrity sha512-2OQsPNEmBCvXuFlIni/a+Rn+R2pHW9INm0BxXJ4hVDA8TirqMj+J/Rp9ItLatT/5pZqWwefVrTQcHpixsxnVlA==

yml-register@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmjs.org/yml-register/-/yml-register-1.2.5.tgz"
  integrity sha512-Elz+U+fPS04GmMn+y8b8Sm7Za99aG+kEMwN08hiHU6WB3VSNXHW5uiJOMKTb3QwZf/WVk2obfh8irMDVHFbkpw==
  dependencies:
    js-yaml "^4.1.0"

zod@^3.19.1:
  version "3.25.67"
  resolved "https://registry.npmjs.org/zod/-/zod-3.25.67.tgz"
  integrity sha512-idA2YXwpCdqUSKRCACDE6ItZD9TZzy3OZMtpfLoh6oPR47lipysRrJfjzMqFxQ3uJuUPyUeWe1r9vLH33xO/Qw==

zod@3.23.8:
  version "3.23.8"
  resolved "https://registry.npmjs.org/zod/-/zod-3.23.8.tgz"
  integrity sha512-XBx9AXhXktjUqnepgTiE5flcKIYWi/rme0Eaj+5Y0lftuGBq+jyRu/md4WnuxqgP1ubdpNCsYEYPxrzVHD8d6g==
